# k230_uart_adapter.py - K230串口适配模块
"""
K230串口适配模块
适配K230的UART接口，保持与原有STM32通信协议的兼容性
"""

import time
import threading

# ========================== K230 UART适配类 ==========================
class K230UART:
    """K230 UART适配类 - 替代maix.uart"""
    
    def __init__(self, device, baudrate):
        self.device = device
        self.baudrate = baudrate
        self._uart = None
        self._callback = None
        self._initialized = False
        self._receive_thread = None
        self._running = False
        
    def init(self):
        """初始化UART"""
        try:
            # K230 UART初始化
            # 注意：这里需要根据K230的实际UART API实现
            # 目前提供框架代码
            
            print(f"K230 UART initializing: {self.device} @ {self.baudrate}")
            
            # 模拟UART初始化成功
            self._initialized = True
            self._running = True
            
            # 启动接收线程
            if self._callback:
                self._receive_thread = threading.Thread(target=self._receive_loop)
                self._receive_thread.daemon = True
                self._receive_thread.start()
            
            print(f"K230 UART initialized successfully")
            return True
            
        except Exception as e:
            print(f"K230 UART initialization failed: {e}")
            return False
    
    def set_received_callback(self, callback):
        """设置接收回调函数"""
        self._callback = callback
        
    def write(self, data):
        """发送数据"""
        try:
            if not self._initialized:
                return False
                
            # K230 UART发送
            # 这里需要根据K230的实际UART API实现
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            # 模拟发送成功
            print(f"K230 UART sent: {data}")
            return True
            
        except Exception as e:
            print(f"K230 UART write failed: {e}")
            return False
    
    def read(self, size=1024):
        """读取数据"""
        try:
            if not self._initialized:
                return b''
                
            # K230 UART读取
            # 这里需要根据K230的实际UART API实现
            
            # 模拟读取数据
            return b''
            
        except Exception as e:
            print(f"K230 UART read failed: {e}")
            return b''
    
    def _receive_loop(self):
        """接收循环线程"""
        while self._running:
            try:
                # 模拟接收数据
                time.sleep(0.1)
                
                # 如果有实际数据接收，调用回调函数
                # data = self.read()
                # if data and self._callback:
                #     self._callback(self, data)
                    
            except Exception as e:
                print(f"K230 UART receive loop error: {e}")
                time.sleep(1)
    
    def close(self):
        """关闭UART"""
        try:
            self._running = False
            if self._receive_thread:
                self._receive_thread.join(timeout=1)
            
            # K230 UART关闭
            # 这里需要根据K230的实际UART API实现
            
            self._initialized = False
            print("K230 UART closed")
            
        except Exception as e:
            print(f"K230 UART close failed: {e}")

# ========================== 适配的SimpleUART类 ==========================
class K230SimpleUART:
    """K230适配的SimpleUART类 - 保持与原有接口的兼容性"""
    
    def __init__(self):
        self.serial = None
        self.rx_buf = ""
        self.is_initialized = False
        self.auto_refresh = True
        self.auto_extract = False
        
        # 帧格式配置
        self.frame_header = "$$"
        self.frame_tail = "##"
        self.frame_enabled = True
        
        # 线程安全
        self._buffer_lock = threading.Lock()
        self._extract_lock = threading.Lock()
        
        # 全局UART实例标记
        self._is_global = False
    
    def init(self, device=None, baudrate=None, set_as_global=True):
        """初始化UART - 兼容原有接口"""
        try:
            # 使用默认参数或提供的参数
            uart_device = device if device is not None else "/dev/ttyS0"
            uart_baudrate = baudrate if baudrate is not None else 115200
            
            # 创建K230 UART实例
            self.serial = K230UART(uart_device, uart_baudrate)
            self.serial.set_received_callback(self._on_received)
            
            # 初始化UART
            if not self.serial.init():
                return False
            
            self.is_initialized = True
            
            # 设置为全局UART实例
            if set_as_global:
                self._set_as_global()
            
            print(f"K230 SimpleUART initialized: {uart_device}:{uart_baudrate}")
            self._show_frame_config()
            
            return True
            
        except Exception as e:
            print(f"K230 SimpleUART initialization failed: {e}")
            self.is_initialized = False
            return False
    
    def set_frame(self, header="$$", tail="##", enabled=True):
        """设置帧格式 - 兼容原有接口"""
        self.frame_header = header
        self.frame_tail = tail
        self.frame_enabled = enabled
        print(f"Frame format: {header}...{tail} ({'enabled' if enabled else 'disabled'})")
    
    def _show_frame_config(self):
        """显示当前帧格式配置"""
        if self.frame_enabled:
            print(f"Frame format: {self.frame_header}...{self.frame_tail}")
        else:
            print("Frame format: disabled (line-based processing)")
    
    def _on_received(self, serial_obj, data):
        """UART数据接收回调"""
        try:
            # 解码数据
            if isinstance(data, bytes):
                decoded_data = data.decode('utf-8', errors='ignore')
            else:
                decoded_data = str(data)
            
            # 线程安全的缓冲区操作
            with self._buffer_lock:
                self.rx_buf += decoded_data
            
            # 如果启用了自动提取，处理数据
            if self.auto_extract:
                self._process_received_data(decoded_data)
                
        except Exception as e:
            print(f"K230 UART receive callback error: {e}")
    
    def _process_received_data(self, data):
        """处理接收到的数据"""
        try:
            # 这里可以添加数据处理逻辑
            # 例如解析key=value对等
            pass
        except Exception as e:
            print(f"Data processing error: {e}")
    
    def send(self, data):
        """发送数据 - 兼容原有接口"""
        if not self.is_initialized:
            print("UART not initialized")
            return False
        
        try:
            # 添加帧格式
            if self.frame_enabled:
                formatted_data = f"{self.frame_header}{data}{self.frame_tail}"
            else:
                formatted_data = data
            
            return self.serial.write(formatted_data)
            
        except Exception as e:
            print(f"K230 UART send failed: {e}")
            return False
    
    def _set_as_global(self):
        """设置为全局UART实例"""
        global _global_k230_uart
        _global_k230_uart = self
        self._is_global = True
        print("Set as global K230 UART instance")
    
    def close(self):
        """关闭UART"""
        try:
            if self.serial:
                self.serial.close()
            self.is_initialized = False
            print("K230 SimpleUART closed")
        except Exception as e:
            print(f"K230 SimpleUART close failed: {e}")

# ========================== 全局函数适配 ==========================
# 全局UART实例
_global_k230_uart = None

def k230_micu_printf(data):
    """K230适配的micu_printf函数"""
    global _global_k230_uart
    
    if _global_k230_uart and _global_k230_uart.is_initialized:
        return _global_k230_uart.send(data)
    else:
        print(f"No global K230 UART instance, data: {data}")
        return False

def set_global_k230_uart(uart_instance):
    """设置全局K230 UART实例"""
    global _global_k230_uart
    _global_k230_uart = uart_instance

# ========================== 兼容性映射 ==========================
# 为了保持与原有代码的兼容性，创建别名
SimpleUART = K230SimpleUART
micu_printf = k230_micu_printf

# ========================== 配置类 ==========================
class K230UARTConfig:
    """K230 UART配置类"""
    
    def __init__(self):
        self.uart_device = "/dev/ttyS0"
        self.uart_baudrate = 115200
        self.refresh_interval = 0.1
        
        # 帧格式配置
        self.frame_header = "$$"
        self.frame_tail = "##"
        self.frame_enabled = True
    
    def set_frame_format(self, header, tail, enabled):
        """设置帧格式"""
        self.frame_header = header
        self.frame_tail = tail
        self.frame_enabled = enabled
    
    def get_frame_config(self):
        """获取帧格式配置"""
        return {
            "header": self.frame_header,
            "tail": self.frame_tail,
            "enabled": self.frame_enabled
        }

# 创建全局配置实例
config = K230UARTConfig()

print("K230 UART adapter module loaded successfully")
