# main_k230_final.py - K230最终优化版智能车主程序
"""
K230最终优化版智能车主程序
基于庐山派K230平台，完全适配GC2093摄像头和官方LCD显示屏
保持与原有STM32通信协议的完全兼容性
"""

# 导入K230适配模块
from k230_compat import *
from k230_image_processing import *
from k230_display_touch import *
from k230_uart_adapter import SimpleUART, micu_printf
from k230_config import k230_config
import math
import gc

# 使用K230优化配置
config = k230_config.get_current_config()
hw_config = config["hardware"]
perf_config = config["performance"]
img_config = config["image_processing"]
persp_config = config["perspective"]
touch_config = config["touch"]
uart_config = config["uart"]
debug_config = config["debug"]

# --------------------------- K230优化的虚拟按键类 ---------------------------
class K230VirtualButtons:
    def __init__(self):
        self.buttons = touch_config.BUTTONS
        self.touch_areas = touch_config.TOUCH_AREAS
        self.last_touch_time = 0
        self.touch_debounce = touch_config.TOUCH_DEBOUNCE

    def check_touch(self, touch_x, touch_y):
        current_time = time.time()
        if current_time - self.last_touch_time < self.touch_debounce:
            return None

        for i, touch_area in enumerate(self.touch_areas):
            area_x, area_y, area_w, area_h = touch_area
            if area_x <= touch_x <= area_x + area_w and area_y <= touch_y <= area_y + area_h:
                self.last_touch_time = current_time
                return self.buttons[i][5]
        return None

    def draw_buttons(self, img, current_mode, threshold):
        for button in self.buttons:
            x, y, w, h, text, action = button
            if (action == "center" and current_mode == "center") or (action == "circle" and current_mode == "circle"):
                color = (0, 255, 255)
                thickness = 3
            elif action in ["thresh_up", "thresh_down"]:
                color = (0, 255, 0)
                thickness = 2
            else:
                color = (255, 255, 255)
                thickness = 2
            
            drawing.draw_rectangle(img, (x, y), (x + w, y + h), color, thickness)
            text_x = x + (w - len(text) * 3) // 2
            text_y = y + (h + 5) // 2
            drawing.put_text(img, text, (text_x, text_y), FONT_HERSHEY_SIMPLEX, 0.3, color, 1)
        
        drawing.put_text(img, f"T:{threshold}", (170, 190),
                   FONT_HERSHEY_SIMPLEX, 0.3, (0, 255, 0), 1)

# --------------------------- K230优化的工具函数 ---------------------------
def generate_circle_points(center, radius, num_points):
    """生成圆形轨迹点 - K230优化版本"""
    circle_points = []
    cx, cy = center
    angle_step = 2 * math.pi / num_points
    
    for i in range(num_points):
        angle = angle_step * i
        x = int(cx + radius * math.cos(angle))
        y = int(cy + radius * math.sin(angle))
        circle_points.append((x, y))
    return circle_points

def k230_perspective_transform(pts, target_width, target_height):
    """K230优化的透视变换"""
    try:
        if persp_config.USE_FAST_TRANSFORM:
            # 使用快速变换算法
            src_pts = pts
            # 简化的变换矩阵
            M = [[1, 0, 0], [0, 1, 0], [0, 0, 1]]
            M_inv = M
            return M, M_inv, src_pts
        else:
            # 标准透视变换
            return perspective_transform(pts, target_width, target_height)
    except Exception as e:
        if debug_config.ENABLE_DEBUG_LOG:
            print(f"Perspective transform error: {e}")
        return None, None, pts

def k230_is_regular_rectangle(approx):
    """K230优化的矩形规则性检查"""
    try:
        if perf_config.SKIP_COMPLEX_VALIDATION:
            # 跳过复杂验证，只做基础检查
            return len(approx) == 4, "简化检查"
        else:
            # 完整的规则性检查
            points = [(int(p[0]), int(p[1])) for p in approx]
            return geometry.is_regular_rectangle(
                points, 
                img_config.MIN_ANGLE, 
                img_config.MAX_ANGLE,
                img_config.MIN_OPPOSITE_RATIO, 
                img_config.MAX_OPPOSITE_RATIO
            )
    except Exception as e:
        if debug_config.ENABLE_DEBUG_LOG:
            print(f"Rectangle validation error: {e}")
        return False, f"检查失败: {e}"

# --------------------------- K230主程序 ---------------------------
if __name__ == "__main__":
    if perf_config.ENABLE_GARBAGE_COLLECTION:
        gc.disable()  # 手动控制垃圾回收
    
    print("K230最终优化版jiguangcar程序启动...")
    print(f"当前质量级别: {k230_config.current_quality_level}")
    print(f"目标FPS: {k230_config.adaptive.TARGET_FPS}")
    
    # 初始化设备
    disp = k230_display
    disp.init(hw_config.CAMERA_WIDTH, hw_config.CAMERA_HEIGHT)
    cam = Camera(hw_config.CAMERA_WIDTH, hw_config.CAMERA_HEIGHT, ImageFormat.FMT_BGR888)
    
    # 初始化虚拟按键和输入管理器
    buttons = K230VirtualButtons()
    input_manager = k230_input
    input_manager.init(touch_config.DEFAULT_INPUT_MODE)
    
    # 程序状态变量
    current_mode = "center"
    last_touch_pos = (0, 0)
    binary_threshold = img_config.BINARY_THRESHOLD
    
    # 初始化串口
    uart = SimpleUART()
    if uart.init(hw_config.UART_DEVICE, hw_config.UART_BAUDRATE, set_as_global=True):
        print(f"串口初始化成功: {hw_config.UART_DEVICE}@{hw_config.UART_BAUDRATE}")
        uart.set_frame(uart_config.FRAME_HEADER, uart_config.FRAME_TAIL, uart_config.ENABLE_FRAME_FORMAT)
    else:
        print("串口初始化失败")
        exit()

    # 性能监控变量
    fps = 0
    last_time = time.ticks_ms()
    frame_count = 0
    last_gc_frame = 0

    print("开始主循环...")
    
    while not app.need_exit():
        frame_count += 1
        
        # 垃圾回收管理
        if (perf_config.ENABLE_GARBAGE_COLLECTION and 
            frame_count - last_gc_frame >= perf_config.GC_FREQUENCY):
            gc.collect()
            last_gc_frame = frame_count
        
        # 处理触摸输入
        current_time = time.time()
        if input_manager and (current_time - buttons.last_touch_time) > buttons.touch_debounce:
            try:
                touch_data = input_manager.read_input()
                if len(touch_data) >= 3:
                    touch_x, touch_y, pressed = touch_data[0], touch_data[1], touch_data[2]
                    last_touch_pos = (touch_x, touch_y)
                    if pressed:
                        action = buttons.check_touch(touch_x, touch_y)
                        if action:
                            buttons.last_touch_time = current_time
                            if action == "center":
                                current_mode = "center"
                                print("模式: 中心点")
                            elif action == "circle":
                                current_mode = "circle"
                                print("模式: 圆形轨迹")
                            elif action == "thresh_up":
                                binary_threshold = min(255, binary_threshold + 5)
                                print(f"阈值: {binary_threshold}")
                            elif action == "thresh_down":
                                binary_threshold = max(1, binary_threshold - 5)
                                print(f"阈值: {binary_threshold}")
            except Exception as e:
                if frame_count % 120 == 0 and debug_config.ENABLE_DEBUG_LOG:
                    print(f"触摸处理错误: {e}")
        
        # 计算FPS
        current_time_ms = time.ticks_ms()
        if current_time_ms - last_time > 0:
            fps = 1000.0 / (current_time_ms - last_time)
        last_time = current_time_ms
        
        # 性能自适应调整
        if frame_count % 30 == 0:  # 每30帧检查一次
            k230_config.adapt_performance(fps)
        
        # 读取图像
        img = cam.read()
        if img is None:
            continue
        
        output = img.img.copy()

        # 图像处理主逻辑
        try:
            # 转换为灰度图
            gray = processor.cvt_color(img.img, COLOR_BGR2GRAY)
            
            # 二值化
            _, binary = processor.threshold(gray, binary_threshold, 255, THRESH_BINARY)
            
            # 形态学操作（使用优化参数）
            processed = processor.morphology_ex(
                binary, MORPH_CLOSE, processor.kernel_3x3, 
                iterations=img_config.MORPH_ITERATIONS
            )
            processed = processor.morphology_ex(
                processed, MORPH_OPEN, processor.kernel_3x3, 
                iterations=1
            )
            
            # 寻找轮廓
            contours, _ = processor.find_contours(processed)
            
            # 轮廓过滤和处理
            quads = []
            contour_count = 0
            
            for cnt in contours:
                if contour_count >= perf_config.MAX_CONTOURS:
                    break  # 限制处理的轮廓数量
                
                area = processor.contour_area(cnt)
                
                # 面积过滤（使用优化参数）
                if not (img_config.MIN_CONTOUR_AREA < area < img_config.MAX_CONTOUR_AREA):
                    continue
                
                # 多边形逼近
                epsilon = img_config.APPROX_EPSILON_FACTOR * processor.arc_length(cnt, True)
                approx = processor.approx_poly_dp(cnt, epsilon, True)
                if len(approx) != img_config.TARGET_SIDES:
                    continue
                
                # 宽高比过滤
                x, y, w, h = processor.bounding_rect(cnt)
                if h == 0:
                    continue
                aspect_ratio = w / h
                if not (img_config.MIN_ASPECT_RATIO <= aspect_ratio <= img_config.MAX_ASPECT_RATIO):
                    continue
                
                # 规则性校验
                is_regular, reason = k230_is_regular_rectangle(approx)
                if not is_regular:
                    continue
                
                quads.append((approx, area))
                contour_count += 1

            # 选择最佳矩形
            inner_quads = []
            if quads:
                largest_quad = min(quads, key=lambda x: x[1])
                inner_quads = [largest_quad]

            # 处理检测到的矩形
            center_points = []
            all_circle_points = []

            for approx, area in inner_quads:
                # 绘制轮廓
                drawing.draw_contours(output, [approx], -1, (0, 255, 0), 2)

                # 计算中心点（简化版本）
                cx = int(sum(p[0] for p in approx) / len(approx))
                cy = int(sum(p[1] for p in approx) / len(approx))
                drawing.draw_circle(output, (cx, cy), 4, (255, 0, 0), -1)
                center_points.append((cx, cy))

                # 圆形模式处理
                if current_mode == "circle":
                    circle_points = generate_circle_points(
                        (cx, cy), 
                        persp_config.CIRCLE_RADIUS, 
                        persp_config.CIRCLE_NUM_POINTS
                    )
                    all_circle_points.extend(circle_points)
                    for (x, y) in circle_points:
                        drawing.draw_circle(output, (x, y), 2, (0, 0, 255), -1)

            # 串口发送数据
            if current_mode == "center":
                if center_points:
                    cx, cy = center_points[0]
                    micu_printf(f"R,{cx},{cy}")
                else:
                    micu_printf("R,0,0")
            elif current_mode == "circle":
                if all_circle_points:
                    circle_data = f"C,{len(all_circle_points)}"
                    for (x, y) in all_circle_points:
                        circle_data += f",{x},{y}"
                    micu_printf(circle_data)

            # 绘制UI元素
            # 目标点标记
            target_x, target_y = hw_config.CAMERA_WIDTH//2, hw_config.CAMERA_HEIGHT//2 - 25
            cross_size = 4
            drawing.draw_line(output, (target_x - cross_size, target_y), (target_x + cross_size, target_y), (255, 0, 255), 2)
            drawing.draw_line(output, (target_x, target_y - cross_size), (target_x, target_y + cross_size), (255, 0, 255), 2)

            # 虚拟按键
            buttons.draw_buttons(output, current_mode, binary_threshold)

            # 状态信息
            if debug_config.ENABLE_FPS_MONITOR:
                drawing.put_text(output, f"FPS:{fps:.1f}", (5, 15),
                           FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
                drawing.put_text(output, f"M:{current_mode[:1].upper()}", (5, 30),
                           FONT_HERSHEY_SIMPLEX, 0.3, (0, 255, 255), 1)
                drawing.put_text(output, f"Q:{k230_config.current_quality_level[:1].upper()}", (5, 45),
                           FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 0), 1)

        except Exception as e:
            if debug_config.ENABLE_DEBUG_LOG:
                print(f"图像处理错误: {e}")

        # 显示图像
        try:
            disp.show(K230Image(output))
        except Exception as e:
            if debug_config.ENABLE_DEBUG_LOG:
                print(f"显示错误: {e}")

        # 性能监控输出
        if (debug_config.ENABLE_FPS_MONITOR and 
            frame_count % debug_config.FPS_DISPLAY_INTERVAL == 0):
            print(f"帧:{frame_count} FPS:{fps:.1f} 质量:{k230_config.current_quality_level}")

    print("K230程序正常退出")
