# basic_camera_test.py - K230基础摄像头测试
"""
K230基础摄像头测试程序
用于验证摄像头和显示功能是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from k230_adapters import *
import time

def test_camera_basic():
    """基础摄像头测试"""
    print("=== K230基础摄像头测试 ===")
    
    try:
        # 初始化摄像头
        print("1. 初始化摄像头...")
        cam = Camera(320, 240, ImageFormat.FMT_BGR888)
        print("   摄像头初始化成功")
        
        # 初始化显示
        print("2. 初始化显示...")
        disp = k230_display
        if disp.init(320, 240):
            print("   显示初始化成功")
        else:
            print("   显示初始化失败")
            return False
        
        # 测试图像读取
        print("3. 测试图像读取...")
        for i in range(10):
            img = cam.read()
            if img:
                print(f"   第{i+1}帧: {img.width()}x{img.height()}")
                # 显示图像
                disp.show(img)
                time.sleep(0.1)
            else:
                print(f"   第{i+1}帧读取失败")
                return False
        
        print("✅ 摄像头测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 摄像头测试失败: {e}")
        return False

def test_image_processing():
    """图像处理测试"""
    print("\n=== K230图像处理测试 ===")
    
    try:
        # 初始化摄像头
        cam = Camera(320, 240, ImageFormat.FMT_BGR888)
        disp = k230_display
        disp.init(320, 240)
        
        print("1. 测试图像处理功能...")
        
        for i in range(5):
            img = cam.read()
            if not img:
                continue
                
            # 转换为灰度图
            gray = processor.cvt_color(img.img, COLOR_BGR2GRAY)
            print(f"   第{i+1}帧: 灰度转换成功")
            
            # 二值化
            _, binary = processor.threshold(gray, 70, 255, THRESH_BINARY)
            print(f"   第{i+1}帧: 二值化成功")
            
            # 寻找轮廓
            contours, _ = processor.find_contours(binary)
            print(f"   第{i+1}帧: 找到{len(contours)}个轮廓")
            
            # 绘制轮廓
            output = img.img.copy()
            for contour in contours[:5]:  # 只绘制前5个轮廓
                area = processor.contour_area(contour)
                if area > 100:
                    drawing.draw_contours(output, [contour], -1, (0, 255, 0), 2)
            
            # 显示结果
            disp.show(K230Image(output))
            time.sleep(0.2)
        
        print("✅ 图像处理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 图像处理测试失败: {e}")
        return False

def test_performance():
    """性能测试"""
    print("\n=== K230性能测试 ===")
    
    try:
        cam = Camera(320, 240, ImageFormat.FMT_BGR888)
        disp = k230_display
        disp.init(320, 240)
        
        print("1. 测试帧率性能...")
        
        frame_count = 0
        start_time = time.time()
        test_duration = 5  # 测试5秒
        
        while time.time() - start_time < test_duration:
            img = cam.read()
            if img:
                # 简单的图像处理
                gray = processor.cvt_color(img.img, COLOR_BGR2GRAY)
                _, binary = processor.threshold(gray, 70, 255, THRESH_BINARY)
                
                # 显示图像
                disp.show(img)
                frame_count += 1
        
        elapsed_time = time.time() - start_time
        fps = frame_count / elapsed_time
        
        print(f"   测试时长: {elapsed_time:.2f}秒")
        print(f"   处理帧数: {frame_count}")
        print(f"   平均FPS: {fps:.2f}")
        
        if fps >= 15:
            print("✅ 性能测试通过 (FPS >= 15)")
            return True
        else:
            print("⚠️  性能测试警告 (FPS < 15)")
            return True  # 仍然算通过，只是性能较低
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def test_config_system():
    """配置系统测试"""
    print("\n=== K230配置系统测试 ===")
    
    try:
        print("1. 测试配置加载...")
        config = k230_config.get_current_config()
        print(f"   当前质量级别: {k230_config.current_quality_level}")
        
        print("2. 测试质量级别切换...")
        for level in ["low", "medium", "high"]:
            k230_config.update_quality_level(level)
            print(f"   切换到{level}级别成功")
        
        print("3. 测试性能自适应...")
        k230_config.adapt_performance(10)  # 低FPS
        print(f"   低FPS适应后级别: {k230_config.current_quality_level}")
        
        k230_config.adapt_performance(30)  # 高FPS
        print(f"   高FPS适应后级别: {k230_config.current_quality_level}")
        
        print("✅ 配置系统测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置系统测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("K230基础功能测试开始...\n")
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("摄像头基础功能", test_camera_basic()))
    test_results.append(("图像处理功能", test_image_processing()))
    test_results.append(("性能测试", test_performance()))
    test_results.append(("配置系统", test_config_system()))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("="*50)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！K230适配层工作正常")
    else:
        print("⚠️  部分测试失败，请检查硬件连接和配置")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        exit(1)
    except Exception as e:
        print(f"\n测试程序异常: {e}")
        exit(1)
