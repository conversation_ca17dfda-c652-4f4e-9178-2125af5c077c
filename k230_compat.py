# k230_compat.py - MaixCAM到K230 CanMV的API适配层
"""
MaixCAM到K230 CanMV的API适配层
提供兼容的接口，使原有的MaixCAM代码能够在K230上运行
"""

import time
import gc
from media.sensor import *
from media.display import *
from media.media import *

# ========================== 图像格式适配 ==========================
class ImageFormat:
    """图像格式适配类"""
    FMT_BGR888 = Sensor.RGB888  # K230使用RGB888替代BGR888
    FMT_RGB565 = Sensor.RGB565
    FMT_GRAYSCALE = Sensor.GRAYSCALE

# ========================== 摄像头适配 ==========================
class Camera:
    """摄像头适配类 - 将maix.camera接口适配到K230 media.sensor"""
    
    def __init__(self, width=320, height=240, format=ImageFormat.FMT_BGR888):
        self.width = width
        self.height = height
        self.format = format
        self.sensor = None
        self._initialized = False
        
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.sensor:
            self.sensor.stop()
            
    def _init_sensor(self):
        """初始化传感器"""
        if self._initialized:
            return
            
        try:
            # 创建传感器对象，默认使用CSI2接口（GC2093）
            self.sensor = Sensor(id=2, width=self.width, height=self.height)
            self.sensor.reset()
            
            # 设置帧大小
            if self.width == 320 and self.height == 240:
                self.sensor.set_framesize(Sensor.QVGA)
            elif self.width == 640 and self.height == 480:
                self.sensor.set_framesize(Sensor.VGA)
            else:
                # 自定义尺寸
                self.sensor.set_framesize(width=self.width, height=self.height)
            
            # 设置像素格式
            if self.format == ImageFormat.FMT_BGR888:
                self.sensor.set_pixformat(Sensor.RGB888)
            elif self.format == ImageFormat.FMT_RGB565:
                self.sensor.set_pixformat(Sensor.RGB565)
            else:
                self.sensor.set_pixformat(Sensor.GRAYSCALE)
            
            # 启动传感器
            self.sensor.run()
            self._initialized = True
            print(f"Camera initialized: {self.width}x{self.height}, format={self.format}")
            
        except Exception as e:
            print(f"Camera initialization failed: {e}")
            raise
    
    def read(self):
        """读取图像 - 兼容maix.camera.read()接口"""
        if not self._initialized:
            self._init_sensor()
            
        try:
            # 获取图像
            img = self.sensor.snapshot()
            return K230Image(img)
        except Exception as e:
            print(f"Camera read failed: {e}")
            return None

# ========================== 图像适配 ==========================
class K230Image:
    """K230图像适配类 - 提供与maix.image兼容的接口"""
    
    def __init__(self, k230_img):
        self.img = k230_img
        self._width = k230_img.width()
        self._height = k230_img.height()
        
    def width(self):
        return self._width
        
    def height(self):
        return self._height
        
    def format(self):
        return self.img.format()
        
    def to_bytes(self):
        """转换为字节数据"""
        return self.img.to_bytes()

# ========================== 显示适配 ==========================
class Display:
    """显示适配类 - 将maix.display接口适配到K230 media.display"""
    
    def __init__(self):
        self._initialized = False
        self._display = None
        
    def _init_display(self, width=320, height=240):
        """初始化显示"""
        if self._initialized:
            return
            
        try:
            # 使用虚拟显示缓冲区
            Display.init(Display.VIRT, width, height)
            MediaManager.init()
            self._initialized = True
            print(f"Display initialized: {width}x{height}")
        except Exception as e:
            print(f"Display initialization failed: {e}")
            raise
    
    def show(self, img):
        """显示图像 - 兼容maix.display.show()接口"""
        if not self._initialized:
            self._init_display(img.width(), img.height())
            
        try:
            if isinstance(img, K230Image):
                Display.show_image(img.img)
            else:
                Display.show_image(img)
        except Exception as e:
            print(f"Display show failed: {e}")

# ========================== 触摸屏适配 ==========================
class TouchScreen:
    """触摸屏适配类"""
    
    def __init__(self):
        self._initialized = False
        
    def read(self):
        """读取触摸数据 - 返回(x, y, pressed)格式"""
        # K230触摸屏接口需要根据具体硬件实现
        # 这里提供基础框架
        try:
            # TODO: 实现K230触摸屏读取
            return (0, 0, False)
        except Exception as e:
            print(f"TouchScreen read failed: {e}")
            return (0, 0, False)

# ========================== 时间适配 ==========================
class Time:
    """时间适配类"""
    
    @staticmethod
    def time():
        return time.time()
        
    @staticmethod
    def ticks_ms():
        return time.ticks_ms()
        
    @staticmethod
    def sleep(seconds):
        time.sleep(seconds)

# ========================== 应用适配 ==========================
class App:
    """应用适配类"""
    
    @staticmethod
    def need_exit():
        """检查是否需要退出"""
        # K230应用退出检查
        return False

# ========================== 图像处理适配 ==========================
def image2cv(img, ensure_bgr=False, copy=False):
    """将K230图像转换为类似OpenCV的格式"""
    # K230不支持OpenCV，这里提供基础的图像数据访问
    if isinstance(img, K230Image):
        return img.to_bytes()
    return img

def cv2image(data, bgr=True, copy=False):
    """将数据转换回K230图像格式"""
    # 这里需要根据K230的具体API实现
    return data

# ========================== 全局实例 ==========================
# 创建全局实例以保持与原代码的兼容性
camera = None
display = Display()
touchscreen = TouchScreen()
app = App()
time = Time()

# 图像处理模块
class image:
    Format = ImageFormat
    
    @staticmethod
    def image2cv(img, ensure_bgr=False, copy=False):
        return image2cv(img, ensure_bgr, copy)
    
    @staticmethod
    def cv2image(data, bgr=True, copy=False):
        return cv2image(data, bgr, copy)

print("K230 compatibility layer loaded successfully")
