# main_k230.py - K230适配版智能车主程序
"""
K230适配版智能车主程序
基于庐山派K230平台，适配GC2093摄像头和官方LCD显示屏
保持与原有STM32通信协议的兼容性
"""

# 导入K230适配模块
from k230_compat import *
from k230_image_processing import *
from k230_display_touch import *
from k230_uart_adapter import SimpleUART, micu_printf
import math
import gc

# --------------------------- 配置参数区（所有过滤条件集中在这里） ---------------------------
# 矩形检测核心参数
MIN_CONTOUR_AREA = 500       # 最小轮廓面积（过滤小目标）
MAX_CONTOUR_AREA = 55000     # 最大轮廓面积（过滤大目标）
TARGET_SIDES = 4             # 目标边数（矩形为4）
BINARY_THRESHOLD = 66        # 二值化阈值

# 宽高比过滤参数（宽/高）
MIN_ASPECT_RATIO = 0.6       # 最小宽高比（高不超过宽的1.67倍）
MAX_ASPECT_RATIO = 1.7       # 最大宽高比（宽不超过高的1.7倍）

# 角度过滤参数（°）
MIN_ANGLE = 75               # 最小直角角度（接近90°）
MAX_ANGLE = 105              # 最大直角角度（接近90°）

# 对边长度一致性参数（比例）
MIN_OPPOSITE_RATIO = 0.6     # 最小对边比例（允许±20%偏差）
MAX_OPPOSITE_RATIO = 1.4     # 最大对边比例

# 透视变换与圆形参数
CORRECTED_WIDTH = 200        # 校正后矩形宽度
CORRECTED_HEIGHT = 150       # 校正后矩形高度
CIRCLE_RADIUS = 50           # 圆形轨迹半径
CIRCLE_NUM_POINTS = 12       # 圆形轨迹点数量

# 触摸按键参数
TOUCH_DEBOUNCE = 0.3         # 触摸防抖动时间（秒）

# --------------------------- 虚拟按键类 ---------------------------
class VirtualButtons:
    def __init__(self):
        self.buttons = [
            [20, 180, 45, 20, "Center", "center"],    
            [115, 180, 50, 20, "Circle", "circle"],   
            [180, 180, 25, 20, "T-", "thresh_down"],  
            [210, 180, 25, 20, "T+", "thresh_up"]     
        ]

        self.touch_areas = [
        [40, 370, 90, 40],         
        [230, 360, 100, 40],
        [360, 360, 50, 40],
        [420, 360, 50, 40]
        ]

        self.last_touch_time = 0
        self.touch_debounce = TOUCH_DEBOUNCE

    def check_touch(self, touch_x, touch_y):
        current_time = time.time()
        if current_time - self.last_touch_time < self.touch_debounce:
            return None

        for i, touch_area in enumerate(self.touch_areas):
            area_x, area_y, area_w, area_h = touch_area
            if area_x <= touch_x <= area_x + area_w and area_y <= touch_y <= area_y + area_h:
                self.last_touch_time = current_time
                return self.buttons[i][5]
        return None

    def draw_buttons(self, img, current_mode, threshold=BINARY_THRESHOLD):
        for button in self.buttons:
            x, y, w, h, text, action = button
            if (action == "center" and current_mode == "center") or (action == "circle" and current_mode == "circle"):
                color = (0, 255, 255)
                thickness = 3
            elif action in ["thresh_up", "thresh_down"]:
                color = (0, 255, 0)
                thickness = 2
            else:
                color = (255, 255, 255)
                thickness = 2
            
            # 使用K230绘图工具
            drawing.draw_rectangle(img, (x, y), (x + w, y + h), color, thickness)
            text_x = x + (w - len(text) * 4) // 2
            text_y = y + (h + 6) // 2
            drawing.put_text(img, text, (text_x, text_y), FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
        
        drawing.put_text(img, f"Thresh: {threshold}", (180, 170),
                   FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)

# 触摸屏初始化
def init_touchscreen():
    try:
        ts = touchscreen
        print("TouchScreen initialized successfully")
        return ts
    except Exception as e:
        print(f"TouchScreen init failed: {e}")
        return None

# --------------------------- 工具函数 ---------------------------
def generate_circle_points(center, radius, num_points):
    circle_points = []
    cx, cy = center
    for i in range(num_points):
        angle = 2 * math.pi * i / num_points
        x = int(cx + radius * math.cos(angle))
        y = int(cy + radius * math.sin(angle))
        circle_points.append((x, y))
    return circle_points

def perspective_transform(pts, target_width, target_height):
    """简化的透视变换"""
    try:
        # 使用K230透视变换工具
        src_pts = pts
        dst_pts = [
            [0, 0], [target_width-1, 0],
            [target_width-1, target_height-1], [0, target_height-1]
        ]
        
        M = perspective.get_perspective_transform(src_pts, dst_pts)
        M_inv = M  # 简化版本
        return M, M_inv, src_pts
    except Exception as e:
        print(f"Perspective transform failed: {e}")
        return None, None, pts

def is_regular_rectangle(approx):
    """使用K230几何工具判断是否为规则矩形"""
    try:
        # 转换为点列表
        points = [(int(p[0]), int(p[1])) for p in approx]
        return geometry.is_regular_rectangle(points, MIN_ANGLE, MAX_ANGLE, 
                                           MIN_OPPOSITE_RATIO, MAX_OPPOSITE_RATIO)
    except Exception as e:
        return False, f"检查失败: {e}"

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    gc.disable()
    print("K230适配版jiguangcar程序启动...")
    print("配置参数加载完成，可在代码开头调整过滤条件")
    
    # 初始化设备
    disp = k230_display
    disp.init(320, 240)
    cam = Camera(320, 240, ImageFormat.FMT_BGR888)

    # 初始化虚拟按键和触摸屏
    buttons = VirtualButtons()
    input_manager = k230_input
    input_manager.init("touch")  # 可以切换为"gpio"使用GPIO按键
    current_mode = "center"
    last_touch_pos = (0, 0)
    binary_threshold = BINARY_THRESHOLD
    
    # 初始化串口
    uart = SimpleUART()
    if uart.init("/dev/ttyS0", 115200, set_as_global=True):
        print("串口初始化成功")
        uart.set_frame("$$", "##", True)
    else:
        print("串口初始化失败")
        exit()

    # FPS计算初始化
    fps = 0
    last_time = time.ticks_ms()
    frame_count = 0

    while not app.need_exit():
        frame_count += 1
        
        # 处理触摸输入
        current_time = time.time()
        if input_manager and (current_time - buttons.last_touch_time) > buttons.touch_debounce:
            try:
                touch_data = input_manager.read_input()
                if len(touch_data) >= 3:
                    touch_x, touch_y, pressed = touch_data[0], touch_data[1], touch_data[2]
                    last_touch_pos = (touch_x, touch_y)
                    if pressed:
                        action = buttons.check_touch(touch_x, touch_y)
                        if action:
                            buttons.last_touch_time = current_time
                            if action == "center":
                                current_mode = "center"
                                print("切换到中心点模式")
                            elif action == "circle":
                                current_mode = "circle"
                                print("切换到圆形模式")
                            elif action == "thresh_up":
                                binary_threshold = min(255, binary_threshold + 3)
                                print(f"阈值增加到: {binary_threshold}")
                            elif action == "thresh_down":
                                binary_threshold = max(1, binary_threshold - 3)
                                print(f"阈值减少到: {binary_threshold}")
            except Exception as e:
                if frame_count % 120 == 0:
                    print(f"Touch processing error: {e}")
        
        # 计算FPS
        current_time_ms = time.ticks_ms()
        if current_time_ms - last_time > 0:
            fps = 1000.0 / (current_time_ms - last_time)
        last_time = current_time_ms
        
        # 读取图像
        img = cam.read()
        if img is None:
            continue

        # 获取原始图像数据
        output = img.img.copy()

        # 1. 矩形检测（使用K230图像处理）
        try:
            # 转换为灰度图
            gray = processor.cvt_color(img.img, COLOR_BGR2GRAY)

            # 二值化
            _, binary = processor.threshold(gray, binary_threshold, 255, THRESH_BINARY)

            # 形态学操作优化
            processed = processor.morphology_ex(binary, MORPH_CLOSE, processor.kernel_3x3, iterations=2)
            processed = processor.morphology_ex(processed, MORPH_OPEN, processor.kernel_3x3, iterations=1)

            # 寻找轮廓
            contours, _ = processor.find_contours(processed)

            quads = []
            for cnt in contours:
                area = processor.contour_area(cnt)
                # 面积过滤
                if not (MIN_CONTOUR_AREA < area < MAX_CONTOUR_AREA):
                    continue

                # 多边形逼近
                epsilon = 0.03 * processor.arc_length(cnt, True)
                approx = processor.approx_poly_dp(cnt, epsilon, True)
                if len(approx) != TARGET_SIDES:
                    continue

                # 宽高比过滤
                x, y, w, h = processor.bounding_rect(cnt)
                if h == 0:
                    continue
                aspect_ratio = w / h
                if not (MIN_ASPECT_RATIO <= aspect_ratio <= MAX_ASPECT_RATIO):
                    continue

                # 规则性校验
                is_regular, reason = is_regular_rectangle(approx)
                if not is_regular:
                    continue

                quads.append((approx, area))

            # 只保留面积最大的矩形
            inner_quads = []
            if quads:
                largest_quad = min(quads, key=lambda x: x[1])
                inner_quads = [largest_quad]

            # 2. 处理检测到的矩形
            center_points = []
            all_circle_points = []

            for approx, area in inner_quads:
                # 绘制轮廓
                drawing.draw_contours(output, [approx], -1, (0, 255, 0), 2)

                # 透视变换获取中心点
                M, M_inv, src_pts = perspective_transform(approx, CORRECTED_WIDTH, CORRECTED_HEIGHT)
                if M_inv is not None:
                    corrected_center = (CORRECTED_WIDTH//2, CORRECTED_HEIGHT//2)
                    # 简化的中心点计算
                    cx = int(sum(p[0] for p in approx) / len(approx))
                    cy = int(sum(p[1] for p in approx) / len(approx))
                    drawing.draw_circle(output, (cx, cy), 5, (255, 0, 0), -1)
                    center_points.append((cx, cy))
                else:
                    cx = int(sum(p[0] for p in approx) / len(approx))
                    cy = int(sum(p[1] for p in approx) / len(approx))
                    drawing.draw_circle(output, (cx, cy), 5, (255, 0, 0), -1)
                    center_points.append((cx, cy))

                # 圆形模式处理
                if current_mode == "circle":
                    if M_inv is not None:
                        # 简化的圆形轨迹生成
                        simple_circle = generate_circle_points((cx, cy), 30, CIRCLE_NUM_POINTS)
                        all_circle_points.extend(simple_circle)
                        for (x, y) in simple_circle:
                            drawing.draw_circle(output, (x, y), 2, (0, 0, 255), -1)

            # 3. 串口发送数据
            if current_mode == "center":
                if center_points:
                    cx, cy = center_points[0]
                    micu_printf(f"R,{cx},{cy}")
                else:
                    micu_printf("R,0,0")
            elif current_mode == "circle":
                if all_circle_points:
                    circle_data = f"C,{len(all_circle_points)}"
                    for (x, y) in all_circle_points:
                        circle_data += f",{x},{y}"
                    micu_printf(circle_data)

            # 4. 绘制目标点标记
            target_x, target_y = 150, 95
            cross_size = 5
            drawing.draw_line(output, (target_x - cross_size, target_y), (target_x + cross_size, target_y), (255, 0, 255), 2)
            drawing.draw_line(output, (target_x, target_y - cross_size), (target_x, target_y + cross_size), (255, 0, 255), 2)
            drawing.put_text(output, f"({target_x},{target_y})", (target_x + 8, target_y - 8),
                       FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)

            # 5. 绘制虚拟按键
            buttons.draw_buttons(output, current_mode, binary_threshold)

            # 6. 显示统计信息
            drawing.put_text(output, f"FPS: {fps:.1f}", (10, 20),
                       FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            mode_text = f"Mode: {current_mode.upper()}"
            drawing.put_text(output, mode_text, (10, 40),
                       FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
            drawing.put_text(output, f"Touch: {last_touch_pos}", (10, 55),
                       FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 0), 1)

        except Exception as e:
            print(f"Image processing error: {e}")

        # 显示图像
        try:
            disp.show(K230Image(output))
        except Exception as e:
            print(f"Display error: {e}")

        if frame_count % 30 == 0:  # 每30帧打印一次FPS
            print(f"Frame {frame_count} processed, FPS: {fps:.1f}")

print("K230适配版程序加载完成")
