# 极光智能车K230移植版

## 项目简介

本项目是将原有MaixCAM平台的极光智能车视觉识别代码完整移植到庐山派K230平台的版本。保持了与STM32设备的完全通信兼容性，并针对K230平台进行了深度优化。

## 项目结构

```
jiguang_car_k230/
├── README.md                    # 项目说明文档
├── main_k230_final.py          # K230最终优化版主程序 (推荐使用)
├── main_k230.py                # K230基础版主程序
├── main.py                     # 原始MaixCAM主程序 (参考)
├── k230_adapters/              # K230适配模块
│   ├── __init__.py
│   ├── k230_compat.py          # API适配层
│   ├── k230_image_processing.py # 图像处理模块
│   ├── k230_display_touch.py   # 显示触摸模块
│   ├── k230_uart_adapter.py    # 串口适配模块
│   └── k230_config.py          # 配置文件
├── micu_uart_lib/              # 原始串口通信库
│   ├── __init__.py
│   ├── config.py
│   ├── simple_uart.py
│   └── utils.py
├── docs/                       # 文档目录
│   ├── README_K230.md          # K230详细使用说明
│   ├── API_Reference.md        # API参考文档
│   └── Troubleshooting.md      # 故障排除指南
├── examples/                   # 示例代码
│   ├── basic_camera_test.py    # 基础摄像头测试
│   ├── uart_test.py           # 串口通信测试
│   └── touch_test.py          # 触摸屏测试
├── config/                     # 配置文件目录
│   ├── default_config.py       # 默认配置
│   └── performance_config.py   # 性能配置
└── tools/                      # 工具脚本
    ├── deploy.py               # 部署脚本
    └── performance_monitor.py  # 性能监控工具
```

## 快速开始

### 1. 环境要求
- 庐山派K230开发板
- CanMV K230固件
- GC2093摄像头
- 官方LCD显示屏

### 2. 安装部署
```bash
# 将整个项目文件夹复制到K230
# 运行主程序
python main_k230_final.py
```

### 3. 功能特性
- ✅ 矩形检测和识别
- ✅ 中心点/圆形轨迹模式
- ✅ 触摸屏控制
- ✅ STM32串口通信兼容
- ✅ 性能自适应优化

## 版本信息

- **版本**: v1.0.0
- **平台**: 庐山派K230
- **固件**: CanMV K230
- **兼容性**: 完全兼容原有STM32通信协议

## 技术支持

详细使用说明请参考 `docs/README_K230.md`

---
**开发团队**: AI智能助手  
**更新时间**: 2025-01-04
