# k230_display_touch.py - K230显示和触摸适配模块
"""
K230显示和触摸适配模块
适配庐山派K230的LCD显示屏和触摸功能
"""

import time
from media.display import *
from media.media import *

# ========================== 显示适配类 ==========================
class K230Display:
    """K230显示适配类"""
    
    def __init__(self):
        self._initialized = False
        self._width = 320
        self._height = 240
        self._display_type = Display.VIRT  # 使用虚拟显示缓冲区
        
    def init(self, width=320, height=240, display_type=None):
        """初始化显示系统"""
        if self._initialized:
            return True
            
        try:
            self._width = width
            self._height = height
            if display_type:
                self._display_type = display_type
            
            # 初始化K230显示系统
            Display.init(self._display_type, self._width, self._height)
            MediaManager.init()
            
            self._initialized = True
            print(f"K230 Display initialized: {self._width}x{self._height}")
            return True
            
        except Exception as e:
            print(f"K230 Display initialization failed: {e}")
            return False
    
    def show(self, img):
        """显示图像"""
        if not self._initialized:
            self.init()
            
        try:
            # 确保图像格式正确
            if hasattr(img, 'img'):
                # K230Image对象
                Display.show_image(img.img)
            else:
                # 直接的图像对象
                Display.show_image(img)
            return True
            
        except Exception as e:
            print(f"Display show failed: {e}")
            return False
    
    def get_size(self):
        """获取显示尺寸"""
        return (self._width, self._height)
    
    def deinit(self):
        """反初始化显示系统"""
        try:
            if self._initialized:
                MediaManager.deinit()
                self._initialized = False
                print("K230 Display deinitialized")
        except Exception as e:
            print(f"Display deinit failed: {e}")

# ========================== 触摸屏适配类 ==========================
class K230TouchScreen:
    """K230触摸屏适配类"""
    
    def __init__(self):
        self._initialized = False
        self._last_touch_time = 0
        self._debounce_time = 0.05  # 50ms防抖
        self._touch_device = None
        
    def init(self):
        """初始化触摸屏"""
        if self._initialized:
            return True
            
        try:
            # K230触摸屏初始化
            # 注意：具体的触摸屏初始化代码需要根据K230的实际API调整
            self._initialized = True
            print("K230 TouchScreen initialized")
            return True
            
        except Exception as e:
            print(f"TouchScreen initialization failed: {e}")
            return False
    
    def read(self):
        """读取触摸数据"""
        if not self._initialized:
            self.init()
            
        try:
            current_time = time.time()
            
            # 防抖处理
            if current_time - self._last_touch_time < self._debounce_time:
                return (0, 0, False)
            
            # K230触摸屏数据读取
            # 注意：这里需要根据K230的实际触摸屏API实现
            # 目前提供模拟实现
            
            # 模拟触摸数据（实际使用时需要替换为真实的K230触摸API）
            touch_x = 0
            touch_y = 0
            pressed = False
            
            # 检查是否有触摸事件
            # 这里应该调用K230的实际触摸检测API
            
            if pressed:
                self._last_touch_time = current_time
            
            return (touch_x, touch_y, pressed)
            
        except Exception as e:
            print(f"TouchScreen read failed: {e}")
            return (0, 0, False)
    
    def available(self):
        """检查是否有触摸数据可用"""
        try:
            # K230触摸数据可用性检查
            # 这里需要根据K230的实际API实现
            return False  # 模拟返回
        except:
            return False
    
    def calibrate(self):
        """触摸屏校准"""
        try:
            # K230触摸屏校准
            print("TouchScreen calibration started...")
            # 这里应该实现K230的触摸屏校准流程
            print("TouchScreen calibration completed")
            return True
        except Exception as e:
            print(f"TouchScreen calibration failed: {e}")
            return False

# ========================== GPIO按键适配类 ==========================
class K230GPIO:
    """K230 GPIO按键适配类 - 作为触摸屏的备用方案"""
    
    def __init__(self):
        self._initialized = False
        self._button_pins = {}
        self._last_press_time = {}
        self._debounce_time = 0.2  # 200ms防抖
        
    def init_button(self, pin, name):
        """初始化GPIO按键"""
        try:
            # K230 GPIO初始化
            # 这里需要根据K230的GPIO API实现
            self._button_pins[name] = pin
            self._last_press_time[name] = 0
            print(f"GPIO button {name} initialized on pin {pin}")
            return True
        except Exception as e:
            print(f"GPIO button init failed: {e}")
            return False
    
    def read_button(self, name):
        """读取GPIO按键状态"""
        if name not in self._button_pins:
            return False
            
        try:
            current_time = time.time()
            
            # 防抖处理
            if current_time - self._last_press_time[name] < self._debounce_time:
                return False
            
            # K230 GPIO读取
            # 这里需要根据K230的GPIO API实现
            pressed = False  # 模拟返回
            
            if pressed:
                self._last_press_time[name] = current_time
                print(f"Button {name} pressed")
                return True
                
            return False
            
        except Exception as e:
            print(f"GPIO button read failed: {e}")
            return False

# ========================== 混合输入管理器 ==========================
class K230InputManager:
    """K230输入管理器 - 统一管理触摸屏和GPIO按键"""
    
    def __init__(self):
        self.touchscreen = K230TouchScreen()
        self.gpio = K230GPIO()
        self._input_mode = "touch"  # "touch" 或 "gpio"
        
    def init(self, input_mode="touch"):
        """初始化输入系统"""
        self._input_mode = input_mode
        
        if input_mode == "touch":
            return self.touchscreen.init()
        elif input_mode == "gpio":
            # 初始化常用的GPIO按键
            success = True
            success &= self.gpio.init_button(0, "center")
            success &= self.gpio.init_button(1, "circle")
            success &= self.gpio.init_button(2, "thresh_up")
            success &= self.gpio.init_button(3, "thresh_down")
            return success
        else:
            print(f"Unknown input mode: {input_mode}")
            return False
    
    def read_input(self):
        """读取输入数据"""
        if self._input_mode == "touch":
            return self.touchscreen.read()
        elif self._input_mode == "gpio":
            # 检查所有GPIO按键
            if self.gpio.read_button("center"):
                return (40, 370, True)  # 模拟触摸坐标
            elif self.gpio.read_button("circle"):
                return (230, 360, True)
            elif self.gpio.read_button("thresh_up"):
                return (420, 360, True)
            elif self.gpio.read_button("thresh_down"):
                return (360, 360, True)
            else:
                return (0, 0, False)
        else:
            return (0, 0, False)
    
    def switch_mode(self, mode):
        """切换输入模式"""
        if mode in ["touch", "gpio"]:
            self._input_mode = mode
            print(f"Input mode switched to: {mode}")
            return self.init(mode)
        else:
            print(f"Invalid input mode: {mode}")
            return False

# ========================== 显示效果增强 ==========================
class K230DisplayEffects:
    """K230显示效果增强类"""
    
    @staticmethod
    def add_border(img, color=(255, 255, 255), thickness=2):
        """添加边框"""
        try:
            width = img.width()
            height = img.height()
            
            # 绘制边框
            img.draw_rectangle(0, 0, width, thickness, color=color, fill=True)  # 上边框
            img.draw_rectangle(0, height-thickness, width, thickness, color=color, fill=True)  # 下边框
            img.draw_rectangle(0, 0, thickness, height, color=color, fill=True)  # 左边框
            img.draw_rectangle(width-thickness, 0, thickness, height, color=color, fill=True)  # 右边框
            
        except Exception as e:
            print(f"Add border failed: {e}")
    
    @staticmethod
    def add_watermark(img, text="K230", position=(10, 10), color=(128, 128, 128)):
        """添加水印"""
        try:
            img.draw_string(position[0], position[1], text, color=color, scale=1)
        except Exception as e:
            print(f"Add watermark failed: {e}")

# ========================== 全局实例 ==========================
# 创建全局实例
k230_display = K230Display()
k230_input = K230InputManager()
k230_effects = K230DisplayEffects()

print("K230 display and touch module loaded successfully")
