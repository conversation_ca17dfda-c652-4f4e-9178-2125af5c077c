# K230移植版故障排除指南

## 常见问题及解决方案

### 1. 摄像头相关问题

#### 问题：摄像头初始化失败
**错误信息**: `Camera initialization failed`

**可能原因**:
- GC2093摄像头未正确连接
- CSI接口配置错误
- 摄像头驱动未加载

**解决方案**:
```python
# 检查摄像头连接
from k230_adapters import Camera
cam = Camera(320, 240)
# 如果失败，尝试不同的CSI接口
# 在k230_compat.py中修改sensor ID
```

#### 问题：图像读取返回None
**错误信息**: `Camera read failed`

**解决方案**:
1. 检查摄像头是否正确初始化
2. 确认摄像头线缆连接牢固
3. 重启K230设备

### 2. 显示相关问题

#### 问题：显示屏无输出
**错误信息**: `Display initialization failed`

**解决方案**:
```python
# 手动初始化显示
from k230_adapters import k230_display
success = k230_display.init(320, 240)
if not success:
    print("显示初始化失败，检查LCD连接")
```

#### 问题：图像显示异常
**可能原因**:
- 图像格式不匹配
- 显示缓冲区问题

**解决方案**:
```python
# 确保图像格式正确
from k230_adapters import K230Image
if isinstance(img, K230Image):
    k230_display.show(img)
else:
    print("图像格式错误")
```

### 3. 触摸屏问题

#### 问题：触摸屏无响应
**错误信息**: `TouchScreen read failed`

**解决方案**:
1. 切换到GPIO按键模式:
```python
from k230_adapters import k230_input
k230_input.switch_mode("gpio")
```

2. 检查触摸屏校准:
```python
k230_input.touchscreen.calibrate()
```

#### 问题：触摸位置不准确
**解决方案**:
- 调整触摸区域配置
- 重新校准触摸屏
- 检查屏幕保护膜是否影响

### 4. 串口通信问题

#### 问题：串口初始化失败
**错误信息**: `UART initialization failed`

**解决方案**:
```python
# 检查串口设备
import os
if os.path.exists("/dev/ttyS0"):
    print("串口设备存在")
else:
    print("串口设备不存在，检查硬件连接")

# 尝试不同的串口设备
uart.init("/dev/ttyS1", 115200)  # 尝试其他串口
```

#### 问题：数据发送失败
**错误信息**: `UART send failed`

**解决方案**:
1. 检查串口连接
2. 验证波特率设置
3. 确认STM32端配置

```python
# 测试串口连接
from k230_adapters import micu_printf
result = micu_printf("TEST")
if not result:
    print("串口发送失败，检查连接")
```

### 5. 性能问题

#### 问题：帧率过低
**现象**: FPS低于15

**解决方案**:
1. 降低质量级别:
```python
from k230_adapters import k230_config
k230_config.update_quality_level("low")
```

2. 启用性能优化:
```python
# 在k230_config.py中调整
PROCESS_EVERY_N_FRAMES = 2  # 每2帧处理一次
ENABLE_GARBAGE_COLLECTION = True
```

3. 减少处理复杂度:
```python
# 调整图像处理参数
MIN_CONTOUR_AREA = 500  # 增大最小面积
MAX_CONTOURS = 5  # 限制轮廓数量
```

#### 问题：内存不足
**错误信息**: `Memory allocation failed`

**解决方案**:
```python
import gc
gc.collect()  # 手动垃圾回收

# 启用自动垃圾回收
from k230_adapters import k230_config
config = k230_config.get_current_config()
config["performance"].ENABLE_GARBAGE_COLLECTION = True
config["performance"].GC_FREQUENCY = 20  # 更频繁的垃圾回收
```

### 6. 图像处理问题

#### 问题：矩形检测失败
**现象**: 无法检测到明显的矩形

**解决方案**:
1. 调整二值化阈值:
```python
# 在程序中动态调整
binary_threshold = 50  # 降低阈值
# 或者使用触摸屏T+/T-按键调整
```

2. 放宽检测条件:
```python
# 在k230_config.py中调整
MIN_CONTOUR_AREA = 200  # 降低最小面积
MIN_ASPECT_RATIO = 0.3  # 放宽宽高比
MAX_ASPECT_RATIO = 3.0
```

3. 优化光照条件:
- 确保充足且均匀的光照
- 避免强烈的阴影和反光

#### 问题：轮廓检测不准确
**解决方案**:
```python
# 调整形态学操作参数
MORPH_ITERATIONS = 2  # 增加迭代次数
MORPH_KERNEL_SIZE = 3  # 增大卷积核
```

### 7. 系统级问题

#### 问题：程序崩溃
**解决方案**:
1. 检查内存使用:
```python
# 启用内存监控
ENABLE_MEMORY_MONITOR = True
MEMORY_CHECK_INTERVAL = 30
```

2. 启用调试模式:
```python
# 在k230_config.py中启用
ENABLE_DEBUG_LOG = True
LOG_LEVEL = "DEBUG"
```

3. 使用异常处理:
```python
try:
    # 主程序逻辑
    main_loop()
except Exception as e:
    print(f"程序异常: {e}")
    # 清理资源
    cleanup()
```

#### 问题：K230系统不稳定
**解决方案**:
1. 更新CanMV固件到最新版本
2. 检查电源供应是否稳定
3. 确认SD卡质量良好

### 8. 调试工具

#### 启用详细日志
```python
from k230_adapters import k230_config
debug_config = k230_config.debug
debug_config.ENABLE_DEBUG_LOG = True
debug_config.LOG_LEVEL = "DEBUG"
```

#### 性能监控
```python
# 启用FPS监控
debug_config.ENABLE_FPS_MONITOR = True
debug_config.FPS_DISPLAY_INTERVAL = 10  # 每10帧显示一次

# 启用内存监控
debug_config.ENABLE_MEMORY_MONITOR = True
```

#### 图像调试
```python
# 保存调试图像
debug_config.ENABLE_IMAGE_DEBUG = True
debug_config.SAVE_DEBUG_IMAGES = True
```

### 9. 测试程序

#### 基础功能测试
```python
# 运行examples/basic_camera_test.py
python examples/basic_camera_test.py
```

#### 串口通信测试
```python
# 运行examples/uart_test.py
python examples/uart_test.py
```

#### 触摸屏测试
```python
# 运行examples/touch_test.py
python examples/touch_test.py
```

### 10. 联系支持

如果以上解决方案都无法解决问题，请：

1. 收集错误日志
2. 记录复现步骤
3. 提供硬件配置信息
4. 描述期望的行为

**调试信息收集**:
```python
# 生成调试报告
from k230_adapters import k230_config
config = k230_config.get_current_config()
print("=== K230调试信息 ===")
print(f"质量级别: {k230_config.current_quality_level}")
print(f"当前FPS: {k230_config.current_fps}")
print(f"硬件配置: {config['hardware'].__dict__}")
print(f"性能配置: {config['performance'].__dict__}")
```

---

**注意**: 大部分问题都可以通过调整配置参数解决。建议先尝试降低质量要求和处理复杂度，确保基础功能正常后再逐步优化。
