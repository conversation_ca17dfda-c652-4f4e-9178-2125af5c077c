# k230_adapters/__init__.py
"""
K230适配模块包
提供MaixCAM到K230 CanMV的完整适配功能
"""

# 导入核心适配模块
from .k230_compat import *
from .k230_image_processing import *
from .k230_display_touch import *
from .k230_uart_adapter import *
from .k230_config import *

__version__ = "1.0.0"
__author__ = "AI Assistant"
__description__ = "MaixCAM to K230 CanMV adaptation layer"

# 模块列表
__all__ = [
    # 兼容性模块
    'Camera', 'K230Image', 'Display', 'TouchScreen', 'Time', 'App',
    'image', 'ImageFormat',
    
    # 图像处理模块
    'K230ImageProcessor', 'GeometryUtils', 'PerspectiveTransform', 'DrawingUtils',
    'processor', 'geometry', 'perspective', 'drawing',
    
    # 显示触摸模块
    'K230Display', 'K230TouchScreen', 'K230GPIO', 'K230InputManager', 'K230DisplayEffects',
    'k230_display', 'k230_input', 'k230_effects',
    
    # 串口适配模块
    'K230UART', 'K230SimpleUART', 'SimpleUART', 'micu_printf',
    
    # 配置模块
    'k230_config', 'K230ConfigManager',
    'K230HardwareConfig', 'K230PerformanceConfig', 'K230ImageProcessingConfig'
]

print("K230 adapters package loaded successfully")
