# K230适配层API参考文档

## 概述

本文档详细介绍了K230适配层提供的所有API接口，帮助开发者理解和使用移植后的功能。

## 核心模块

### 1. k230_compat.py - 兼容性适配层

#### Camera类
```python
class Camera:
    def __init__(self, width=320, height=240, format=ImageFormat.FMT_BGR888)
    def read()  # 返回K230Image对象
```

#### K230Image类
```python
class K230Image:
    def width()
    def height()
    def format()
    def to_bytes()
```

#### Display类
```python
class Display:
    def show(img)  # 显示图像
```

### 2. k230_image_processing.py - 图像处理模块

#### K230ImageProcessor类
```python
class K230ImageProcessor:
    def threshold(img, thresh_value, max_value=255, thresh_type=0)
    def find_contours(binary_img)
    def contour_area(contour)
    def arc_length(contour, closed=True)
    def approx_poly_dp(contour, epsilon, closed=True)
    def bounding_rect(contour)
    def is_contour_convex(contour)
    def morphology_ex(img, operation, kernel, iterations=1)
    def cvt_color(img, color_code)
```

#### GeometryUtils类
```python
class GeometryUtils:
    @staticmethod
    def calculate_distance(p1, p2)
    @staticmethod
    def calculate_angle(p1, p2, p3)
    @staticmethod
    def is_regular_rectangle(points, min_angle=75, max_angle=105, 
                           min_ratio=0.6, max_ratio=1.4)
```

#### DrawingUtils类
```python
class DrawingUtils:
    @staticmethod
    def draw_contours(img, contours, contour_idx=-1, color=(0, 255, 0), thickness=2)
    @staticmethod
    def draw_circle(img, center, radius, color=(255, 0, 0), thickness=-1)
    @staticmethod
    def draw_rectangle(img, pt1, pt2, color=(255, 255, 255), thickness=2)
    @staticmethod
    def put_text(img, text, org, font, font_scale, color, thickness=1)
    @staticmethod
    def draw_line(img, pt1, pt2, color=(255, 0, 255), thickness=2)
```

### 3. k230_display_touch.py - 显示触摸模块

#### K230Display类
```python
class K230Display:
    def init(width=320, height=240, display_type=None)
    def show(img)
    def get_size()
    def deinit()
```

#### K230InputManager类
```python
class K230InputManager:
    def init(input_mode="touch")  # "touch" 或 "gpio"
    def read_input()  # 返回(x, y, pressed)
    def switch_mode(mode)
```

### 4. k230_uart_adapter.py - 串口适配模块

#### K230SimpleUART类
```python
class K230SimpleUART:
    def init(device=None, baudrate=None, set_as_global=True)
    def set_frame(header="$$", tail="##", enabled=True)
    def send(data)
    def close()
```

#### 全局函数
```python
def micu_printf(data)  # 发送格式化数据
```

### 5. k230_config.py - 配置模块

#### K230ConfigManager类
```python
class K230ConfigManager:
    def get_current_config()
    def update_quality_level(level)  # "high", "medium", "low"
    def adapt_performance(current_fps)
```

## 配置参数

### 硬件配置
```python
K230HardwareConfig:
    CAMERA_WIDTH = 320
    CAMERA_HEIGHT = 240
    CAMERA_FPS = 30
    UART_DEVICE = "/dev/ttyS0"
    UART_BAUDRATE = 115200
```

### 图像处理配置
```python
K230ImageProcessingConfig:
    MIN_CONTOUR_AREA = 300
    MAX_CONTOUR_AREA = 30000
    BINARY_THRESHOLD = 70
    MIN_ASPECT_RATIO = 0.5
    MAX_ASPECT_RATIO = 2.0
```

### 性能配置
```python
K230PerformanceConfig:
    ENABLE_MULTI_THREADING = False
    PROCESS_EVERY_N_FRAMES = 1
    ENABLE_GARBAGE_COLLECTION = True
    GC_FREQUENCY = 30
```

## 使用示例

### 基础摄像头使用
```python
from k230_adapters import *

# 初始化摄像头
cam = Camera(320, 240, ImageFormat.FMT_BGR888)

# 读取图像
img = cam.read()
if img:
    print(f"图像尺寸: {img.width()}x{img.height()}")
```

### 图像处理
```python
from k230_adapters import processor, drawing

# 图像处理
gray = processor.cvt_color(img.img, "COLOR_BGR2GRAY")
_, binary = processor.threshold(gray, 70, 255, 0)
contours, _ = processor.find_contours(binary)

# 绘制结果
for contour in contours:
    area = processor.contour_area(contour)
    if area > 500:
        drawing.draw_contours(img.img, [contour], -1, (0, 255, 0), 2)
```

### 显示和触摸
```python
from k230_adapters import k230_display, k230_input

# 初始化显示和输入
k230_display.init(320, 240)
k230_input.init("touch")

# 显示图像
k230_display.show(img)

# 读取触摸
touch_x, touch_y, pressed = k230_input.read_input()
if pressed:
    print(f"触摸位置: ({touch_x}, {touch_y})")
```

### 串口通信
```python
from k230_adapters import SimpleUART, micu_printf

# 初始化串口
uart = SimpleUART()
uart.init("/dev/ttyS0", 115200, set_as_global=True)
uart.set_frame("$$", "##", True)

# 发送数据
micu_printf("R,160,120")  # 发送中心点坐标
```

## 常量定义

### 图像格式
```python
ImageFormat.FMT_BGR888
ImageFormat.FMT_RGB565
ImageFormat.FMT_GRAYSCALE
```

### 图像处理常量
```python
THRESH_BINARY = 0
MORPH_CLOSE = "MORPH_CLOSE"
MORPH_OPEN = "MORPH_OPEN"
COLOR_BGR2GRAY = "COLOR_BGR2GRAY"
FONT_HERSHEY_SIMPLEX = 0
```

## 错误处理

所有API都包含异常处理，在出错时会：
1. 打印错误信息（如果启用调试）
2. 返回安全的默认值
3. 继续程序执行而不崩溃

## 性能优化建议

1. **使用适当的图像尺寸**: 320x240是推荐的平衡点
2. **启用自适应性能**: 让系统自动调整质量级别
3. **合理设置处理频率**: 不是每帧都需要完整处理
4. **及时释放资源**: 使用完毕后调用相应的清理函数

## 兼容性说明

- ✅ 完全兼容原有MaixCAM接口
- ✅ 保持STM32通信协议不变
- ❌ 不支持OpenCV原生函数
- ❌ 部分高级图像处理功能简化实现
