# uart_test.py - K230串口通信测试
"""
K230串口通信测试程序
用于验证与STM32的通信功能是否正常
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from k230_adapters import SimpleUART, micu_printf
import time

def test_uart_init():
    """测试串口初始化"""
    print("=== K230串口初始化测试 ===")
    
    try:
        print("1. 测试默认串口初始化...")
        uart = SimpleUART()
        result = uart.init("/dev/ttyS0", 115200, set_as_global=True)
        
        if result:
            print("   ✅ 串口初始化成功")
            return uart
        else:
            print("   ❌ 串口初始化失败")
            return None
            
    except Exception as e:
        print(f"   ❌ 串口初始化异常: {e}")
        return None

def test_frame_format(uart):
    """测试帧格式设置"""
    print("\n=== K230帧格式测试 ===")
    
    try:
        print("1. 测试默认帧格式...")
        uart.set_frame("$$", "##", True)
        print("   ✅ 默认帧格式设置成功")
        
        print("2. 测试自定义帧格式...")
        uart.set_frame("<<", ">>", True)
        print("   ✅ 自定义帧格式设置成功")
        
        # 恢复默认格式
        uart.set_frame("$$", "##", True)
        print("   ✅ 恢复默认帧格式")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 帧格式测试失败: {e}")
        return False

def test_data_sending(uart):
    """测试数据发送"""
    print("\n=== K230数据发送测试 ===")
    
    try:
        print("1. 测试中心点数据发送...")
        
        # 测试中心点数据
        test_data = [
            "R,160,120",  # 中心点
            "R,100,80",   # 左上
            "R,220,160",  # 右下
            "R,0,0"       # 无目标
        ]
        
        for i, data in enumerate(test_data):
            result = uart.send(data)
            if result:
                print(f"   第{i+1}条数据发送成功: {data}")
            else:
                print(f"   第{i+1}条数据发送失败: {data}")
                return False
            time.sleep(0.1)
        
        print("2. 测试圆形轨迹数据发送...")
        
        # 测试圆形轨迹数据
        circle_data = "C,8,150,100,170,110,180,130,170,150,150,160,130,150,120,130,130,110"
        result = uart.send(circle_data)
        if result:
            print(f"   圆形轨迹数据发送成功")
        else:
            print(f"   圆形轨迹数据发送失败")
            return False
        
        print("✅ 数据发送测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据发送测试失败: {e}")
        return False

def test_micu_printf():
    """测试全局micu_printf函数"""
    print("\n=== K230全局函数测试 ===")
    
    try:
        print("1. 测试micu_printf函数...")
        
        test_messages = [
            "R,160,120",
            "C,4,100,100,200,100,200,200,100,200",
            "STATUS,OK",
            "TEST,123"
        ]
        
        for i, msg in enumerate(test_messages):
            result = micu_printf(msg)
            if result:
                print(f"   第{i+1}条消息发送成功: {msg}")
            else:
                print(f"   第{i+1}条消息发送失败: {msg}")
                return False
            time.sleep(0.1)
        
        print("✅ 全局函数测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 全局函数测试失败: {e}")
        return False

def test_protocol_compatibility():
    """测试协议兼容性"""
    print("\n=== K230协议兼容性测试 ===")
    
    try:
        print("1. 测试STM32兼容协议...")
        
        # 模拟实际使用场景的数据
        scenarios = [
            {
                "name": "中心点模式",
                "data": ["R,160,120", "R,150,110", "R,170,130", "R,0,0"]
            },
            {
                "name": "圆形轨迹模式",
                "data": [
                    "C,6,160,120,180,130,190,150,180,170,160,180,140,170",
                    "C,8,150,100,170,110,180,130,170,150,150,160,130,150,120,130,130,110"
                ]
            },
            {
                "name": "混合模式",
                "data": ["R,160,120", "C,4,100,100,200,100,200,200,100,200", "R,0,0"]
            }
        ]
        
        for scenario in scenarios:
            print(f"   测试{scenario['name']}...")
            for data in scenario['data']:
                result = micu_printf(data)
                if not result:
                    print(f"     ❌ 数据发送失败: {data}")
                    return False
                time.sleep(0.05)
            print(f"     ✅ {scenario['name']}测试通过")
        
        print("✅ 协议兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 协议兼容性测试失败: {e}")
        return False

def test_performance_stress():
    """测试性能压力"""
    print("\n=== K230串口性能测试 ===")
    
    try:
        print("1. 测试高频数据发送...")
        
        start_time = time.time()
        send_count = 0
        test_duration = 3  # 测试3秒
        
        while time.time() - start_time < test_duration:
            # 模拟高频发送
            data = f"R,{160 + send_count % 20},{120 + send_count % 15}"
            result = micu_printf(data)
            if result:
                send_count += 1
            time.sleep(0.01)  # 100Hz发送频率
        
        elapsed_time = time.time() - start_time
        send_rate = send_count / elapsed_time
        
        print(f"   测试时长: {elapsed_time:.2f}秒")
        print(f"   发送次数: {send_count}")
        print(f"   发送频率: {send_rate:.2f} Hz")
        
        if send_rate >= 50:  # 期望至少50Hz
            print("   ✅ 性能测试通过 (频率 >= 50Hz)")
            return True
        else:
            print("   ⚠️  性能测试警告 (频率 < 50Hz)")
            return True  # 仍然算通过
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n=== K230错误处理测试 ===")
    
    try:
        print("1. 测试无效数据处理...")
        
        # 测试各种边界情况
        invalid_data = [
            "",  # 空数据
            "R",  # 不完整数据
            "R,abc,def",  # 非数字数据
            "X,160,120",  # 无效命令
            "R," + "1" * 1000,  # 超长数据
        ]
        
        for data in invalid_data:
            try:
                result = micu_printf(data)
                print(f"   无效数据处理: {data[:20]}... -> {'成功' if result else '失败'}")
            except Exception as e:
                print(f"   无效数据异常: {data[:20]}... -> {e}")
        
        print("2. 测试串口断开恢复...")
        # 这里可以添加串口断开重连的测试
        
        print("✅ 错误处理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("K230串口通信测试开始...\n")
    
    # 初始化串口
    uart = test_uart_init()
    if not uart:
        print("❌ 串口初始化失败，无法继续测试")
        return False
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("帧格式设置", test_frame_format(uart)))
    test_results.append(("数据发送", test_data_sending(uart)))
    test_results.append(("全局函数", test_micu_printf()))
    test_results.append(("协议兼容性", test_protocol_compatibility()))
    test_results.append(("性能压力", test_performance_stress()))
    test_results.append(("错误处理", test_error_handling()))
    
    # 清理资源
    try:
        uart.close()
        print("\n串口资源清理完成")
    except:
        pass
    
    # 输出测试结果
    print("\n" + "="*50)
    print("串口通信测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("="*50)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有串口测试通过！与STM32通信兼容")
    else:
        print("⚠️  部分测试失败，请检查串口连接和STM32配置")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        exit(1)
    except Exception as e:
        print(f"\n测试程序异常: {e}")
        exit(1)
