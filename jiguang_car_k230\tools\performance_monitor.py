# performance_monitor.py - K230性能监控工具
"""
K230性能监控工具
用于监控和分析K230智能车程序的性能表现
"""

import time
import gc
import sys
import os
from collections import deque
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

class K230PerformanceMonitor:
    """K230性能监控器"""
    
    def __init__(self, history_size=100):
        self.history_size = history_size
        self.fps_history = deque(maxlen=history_size)
        self.memory_history = deque(maxlen=history_size)
        self.frame_time_history = deque(maxlen=history_size)
        
        self.start_time = time.time()
        self.last_frame_time = time.time()
        self.frame_count = 0
        
        self.monitoring = False
        self.log_file = None
    
    def start_monitoring(self, log_to_file=True):
        """开始性能监控"""
        self.monitoring = True
        self.start_time = time.time()
        self.frame_count = 0
        
        if log_to_file:
            log_filename = f"k230_performance_{time.strftime('%Y%m%d_%H%M%S')}.log"
            self.log_file = open(log_filename, 'w', encoding='utf-8')
            self.log(f"性能监控开始 - {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        print("🚀 K230性能监控已启动")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.monitoring = False
        
        if self.log_file:
            self.log(f"性能监控结束 - {time.strftime('%Y-%m-%d %H:%M:%S')}")
            self.log_file.close()
            self.log_file = None
        
        print("⏹️  K230性能监控已停止")
    
    def log(self, message):
        """记录日志"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        
        if self.log_file:
            self.log_file.write(log_entry + "\n")
            self.log_file.flush()
        
        print(log_entry)
    
    def update_frame(self):
        """更新帧统计"""
        if not self.monitoring:
            return
        
        current_time = time.time()
        
        # 计算帧时间
        frame_time = current_time - self.last_frame_time
        self.frame_time_history.append(frame_time)
        
        # 计算FPS
        if frame_time > 0:
            fps = 1.0 / frame_time
            self.fps_history.append(fps)
        
        # 更新计数器
        self.frame_count += 1
        self.last_frame_time = current_time
    
    def update_memory(self):
        """更新内存统计"""
        if not self.monitoring:
            return
        
        try:
            # 获取内存信息（简化版本）
            gc.collect()  # 强制垃圾回收
            
            # 这里应该获取实际的内存使用情况
            # K230平台可能需要特定的内存监控方法
            memory_usage = 0  # 占位符
            self.memory_history.append(memory_usage)
            
        except Exception as e:
            self.log(f"内存监控错误: {e}")
    
    def get_current_stats(self):
        """获取当前统计信息"""
        stats = {
            "frame_count": self.frame_count,
            "running_time": time.time() - self.start_time,
            "current_fps": 0,
            "average_fps": 0,
            "min_fps": 0,
            "max_fps": 0,
            "frame_time_avg": 0,
            "frame_time_min": 0,
            "frame_time_max": 0
        }
        
        if self.fps_history:
            stats["current_fps"] = self.fps_history[-1]
            stats["average_fps"] = sum(self.fps_history) / len(self.fps_history)
            stats["min_fps"] = min(self.fps_history)
            stats["max_fps"] = max(self.fps_history)
        
        if self.frame_time_history:
            stats["frame_time_avg"] = sum(self.frame_time_history) / len(self.frame_time_history)
            stats["frame_time_min"] = min(self.frame_time_history)
            stats["frame_time_max"] = max(self.frame_time_history)
        
        return stats
    
    def print_stats(self):
        """打印统计信息"""
        stats = self.get_current_stats()
        
        print("\n" + "="*50)
        print("K230性能统计")
        print("="*50)
        print(f"运行时间: {stats['running_time']:.2f}秒")
        print(f"总帧数: {stats['frame_count']}")
        print(f"当前FPS: {stats['current_fps']:.2f}")
        print(f"平均FPS: {stats['average_fps']:.2f}")
        print(f"FPS范围: {stats['min_fps']:.2f} - {stats['max_fps']:.2f}")
        print(f"平均帧时间: {stats['frame_time_avg']*1000:.2f}ms")
        print(f"帧时间范围: {stats['frame_time_min']*1000:.2f} - {stats['frame_time_max']*1000:.2f}ms")
        print("="*50)
    
    def generate_report(self):
        """生成性能报告"""
        stats = self.get_current_stats()
        
        report = f"""# K230智能车性能报告

## 基本信息
- 监控时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
- 运行时长: {stats['running_time']:.2f}秒
- 总帧数: {stats['frame_count']}

## FPS性能
- 当前FPS: {stats['current_fps']:.2f}
- 平均FPS: {stats['average_fps']:.2f}
- 最低FPS: {stats['min_fps']:.2f}
- 最高FPS: {stats['max_fps']:.2f}

## 帧时间分析
- 平均帧时间: {stats['frame_time_avg']*1000:.2f}ms
- 最短帧时间: {stats['frame_time_min']*1000:.2f}ms
- 最长帧时间: {stats['frame_time_max']*1000:.2f}ms

## 性能评估
"""
        
        # 性能评估
        avg_fps = stats['average_fps']
        if avg_fps >= 25:
            report += "- ✅ 性能优秀 (FPS >= 25)\n"
        elif avg_fps >= 20:
            report += "- ✅ 性能良好 (FPS >= 20)\n"
        elif avg_fps >= 15:
            report += "- ⚠️  性能一般 (FPS >= 15)\n"
        else:
            report += "- ❌ 性能不足 (FPS < 15)\n"
        
        # FPS稳定性评估
        if self.fps_history:
            fps_std = (sum((x - stats['average_fps'])**2 for x in self.fps_history) / len(self.fps_history))**0.5
            if fps_std < 2:
                report += "- ✅ FPS稳定性优秀\n"
            elif fps_std < 5:
                report += "- ✅ FPS稳定性良好\n"
            else:
                report += "- ⚠️  FPS波动较大\n"
        
        report += f"""
## 优化建议
"""
        
        if avg_fps < 20:
            report += "- 考虑降低图像处理质量级别\n"
            report += "- 减少处理的轮廓数量\n"
            report += "- 启用性能优化选项\n"
        
        if stats['frame_time_max'] > 0.1:  # 100ms
            report += "- 存在帧时间过长的情况，检查算法复杂度\n"
        
        return report
    
    def save_report(self, filename=None):
        """保存性能报告"""
        if filename is None:
            filename = f"k230_performance_report_{time.strftime('%Y%m%d_%H%M%S')}.md"
        
        report = self.generate_report()
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)
        
        self.log(f"性能报告已保存: {filename}")
        return filename

class K230PerformanceProfiler:
    """K230性能分析器"""
    
    def __init__(self):
        self.function_times = {}
        self.call_counts = {}
    
    def profile_function(self, func_name):
        """函数性能分析装饰器"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                start_time = time.time()
                result = func(*args, **kwargs)
                end_time = time.time()
                
                execution_time = end_time - start_time
                
                if func_name not in self.function_times:
                    self.function_times[func_name] = []
                    self.call_counts[func_name] = 0
                
                self.function_times[func_name].append(execution_time)
                self.call_counts[func_name] += 1
                
                return result
            return wrapper
        return decorator
    
    def get_profile_report(self):
        """获取性能分析报告"""
        report = "# 函数性能分析报告\n\n"
        
        for func_name in self.function_times:
            times = self.function_times[func_name]
            count = self.call_counts[func_name]
            
            avg_time = sum(times) / len(times)
            min_time = min(times)
            max_time = max(times)
            total_time = sum(times)
            
            report += f"## {func_name}\n"
            report += f"- 调用次数: {count}\n"
            report += f"- 总耗时: {total_time*1000:.2f}ms\n"
            report += f"- 平均耗时: {avg_time*1000:.2f}ms\n"
            report += f"- 最短耗时: {min_time*1000:.2f}ms\n"
            report += f"- 最长耗时: {max_time*1000:.2f}ms\n\n"
        
        return report

def monitor_main_program():
    """监控主程序性能"""
    print("K230智能车性能监控工具")
    print("=" * 40)
    
    monitor = K230PerformanceMonitor()
    
    try:
        # 启动监控
        monitor.start_monitoring()
        
        # 模拟主程序运行
        print("开始监控主程序性能...")
        print("按 Ctrl+C 停止监控")
        
        while True:
            # 模拟帧处理
            time.sleep(0.04)  # 模拟25FPS
            monitor.update_frame()
            monitor.update_memory()
            
            # 每30帧打印一次统计
            if monitor.frame_count % 30 == 0:
                stats = monitor.get_current_stats()
                print(f"帧数: {stats['frame_count']}, "
                      f"当前FPS: {stats['current_fps']:.1f}, "
                      f"平均FPS: {stats['average_fps']:.1f}")
    
    except KeyboardInterrupt:
        print("\n监控被用户中断")
    
    finally:
        # 停止监控并生成报告
        monitor.stop_monitoring()
        monitor.print_stats()
        
        report_file = monitor.save_report()
        print(f"\n性能报告已保存: {report_file}")

if __name__ == "__main__":
    monitor_main_program()
