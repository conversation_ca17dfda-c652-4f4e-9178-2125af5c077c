# k230_config.py - K230优化配置文件
"""
K230优化配置文件
根据庐山派K230的性能特点优化图像处理参数和算法配置
"""

# ========================== 硬件配置 ==========================
class K230HardwareConfig:
    """K230硬件配置"""
    
    # 摄像头配置
    CAMERA_WIDTH = 320
    CAMERA_HEIGHT = 240
    CAMERA_FPS = 30
    CAMERA_FORMAT = "RGB888"  # K230支持的格式
    
    # 显示配置
    DISPLAY_WIDTH = 320
    DISPLAY_HEIGHT = 240
    DISPLAY_TYPE = "VIRT"  # 虚拟显示缓冲区
    
    # 串口配置
    UART_DEVICE = "/dev/ttyS0"
    UART_BAUDRATE = 115200
    UART_FRAME_HEADER = "$$"
    UART_FRAME_TAIL = "##"
    
    # GPIO配置（如果使用GPIO按键）
    GPIO_BUTTON_PINS = {
        "center": 0,
        "circle": 1,
        "thresh_up": 2,
        "thresh_down": 3
    }

# ========================== 性能优化配置 ==========================
class K230PerformanceConfig:
    """K230性能优化配置"""
    
    # 图像处理优化
    ENABLE_MULTI_THREADING = False  # K230单核处理，关闭多线程
    IMAGE_BUFFER_SIZE = 2  # 图像缓冲区大小
    PROCESS_EVERY_N_FRAMES = 1  # 每N帧处理一次（1=每帧都处理）
    
    # 内存优化
    ENABLE_GARBAGE_COLLECTION = True  # 启用垃圾回收
    GC_FREQUENCY = 30  # 每30帧执行一次垃圾回收
    MAX_CONTOURS = 10  # 最大轮廓数量限制
    
    # 算法优化
    USE_SIMPLIFIED_PERSPECTIVE = True  # 使用简化的透视变换
    SKIP_COMPLEX_VALIDATION = False  # 跳过复杂的验证步骤
    ENABLE_EARLY_TERMINATION = True  # 启用早期终止优化

# ========================== 图像处理配置 ==========================
class K230ImageProcessingConfig:
    """K230图像处理配置 - 针对K230优化"""
    
    # 矩形检测核心参数（针对K230优化）
    MIN_CONTOUR_AREA = 300       # 降低最小面积以适应K230性能
    MAX_CONTOUR_AREA = 30000     # 降低最大面积以减少计算量
    TARGET_SIDES = 4             # 目标边数（矩形为4）
    BINARY_THRESHOLD = 70        # 针对K230摄像头优化的阈值
    
    # 宽高比过滤参数（放宽限制以提高检测率）
    MIN_ASPECT_RATIO = 0.5       # 放宽最小宽高比
    MAX_ASPECT_RATIO = 2.0       # 放宽最大宽高比
    
    # 角度过滤参数（放宽限制）
    MIN_ANGLE = 70               # 放宽最小角度
    MAX_ANGLE = 110              # 放宽最大角度
    
    # 对边长度一致性参数（放宽限制）
    MIN_OPPOSITE_RATIO = 0.5     # 放宽最小对边比例
    MAX_OPPOSITE_RATIO = 1.5     # 放宽最大对边比例
    
    # 形态学操作参数（简化以提高性能）
    MORPH_KERNEL_SIZE = 2        # 减小卷积核大小
    MORPH_ITERATIONS = 1         # 减少迭代次数
    
    # 轮廓近似参数
    APPROX_EPSILON_FACTOR = 0.02  # 降低精度要求以提高性能

# ========================== 透视变换配置 ==========================
class K230PerspectiveConfig:
    """K230透视变换配置"""
    
    # 校正后图像尺寸（减小以提高性能）
    CORRECTED_WIDTH = 150
    CORRECTED_HEIGHT = 100
    
    # 圆形轨迹参数（减少点数以提高性能）
    CIRCLE_RADIUS = 40
    CIRCLE_NUM_POINTS = 8  # 减少圆形轨迹点数
    
    # 透视变换优化
    USE_FAST_TRANSFORM = True  # 使用快速变换算法
    CACHE_TRANSFORM_MATRIX = True  # 缓存变换矩阵

# ========================== 触摸控制配置 ==========================
class K230TouchConfig:
    """K230触摸控制配置"""
    
    # 触摸防抖参数
    TOUCH_DEBOUNCE = 0.2         # 增加防抖时间以避免误触
    
    # 虚拟按键配置（针对K230屏幕优化）
    BUTTONS = [
        [15, 200, 40, 18, "Center", "center"],    
        [100, 200, 45, 18, "Circle", "circle"],   
        [170, 200, 20, 18, "T-", "thresh_down"],  
        [195, 200, 20, 18, "T+", "thresh_up"]     
    ]
    
    # 触摸区域配置（针对K230屏幕优化）
    TOUCH_AREAS = [
        [30, 350, 80, 35],         
        [200, 340, 90, 35],
        [320, 340, 45, 35],
        [370, 340, 45, 35]
    ]
    
    # 输入模式配置
    DEFAULT_INPUT_MODE = "touch"  # "touch" 或 "gpio"
    ENABLE_INPUT_FALLBACK = True  # 启用输入模式回退

# ========================== 串口通信配置 ==========================
class K230UARTConfig:
    """K230串口通信配置"""
    
    # 通信协议配置
    FRAME_HEADER = "$$"
    FRAME_TAIL = "##"
    ENABLE_FRAME_FORMAT = True
    
    # 数据发送配置
    SEND_FREQUENCY = 30  # 发送频率（Hz）
    MAX_DATA_LENGTH = 256  # 最大数据长度
    
    # 错误处理配置
    ENABLE_ERROR_RECOVERY = True
    MAX_RETRY_COUNT = 3
    RETRY_DELAY = 0.1  # 重试延迟（秒）

# ========================== 调试配置 ==========================
class K230DebugConfig:
    """K230调试配置"""
    
    # 日志配置
    ENABLE_DEBUG_LOG = True
    LOG_LEVEL = "INFO"  # "DEBUG", "INFO", "WARNING", "ERROR"
    
    # 性能监控
    ENABLE_FPS_MONITOR = True
    FPS_DISPLAY_INTERVAL = 30  # 每30帧显示一次FPS
    
    # 图像调试
    ENABLE_IMAGE_DEBUG = False  # 关闭图像调试以提高性能
    SAVE_DEBUG_IMAGES = False
    
    # 内存监控
    ENABLE_MEMORY_MONITOR = True
    MEMORY_CHECK_INTERVAL = 60  # 每60帧检查一次内存

# ========================== 自适应配置 ==========================
class K230AdaptiveConfig:
    """K230自适应配置"""
    
    # 性能自适应
    ENABLE_ADAPTIVE_PERFORMANCE = True
    TARGET_FPS = 25  # 目标FPS
    MIN_FPS = 15     # 最低可接受FPS
    
    # 质量自适应
    ENABLE_ADAPTIVE_QUALITY = True
    QUALITY_LEVELS = {
        "high": {
            "min_area": 500,
            "max_area": 50000,
            "morph_iterations": 2,
            "circle_points": 12
        },
        "medium": {
            "min_area": 400,
            "max_area": 40000,
            "morph_iterations": 1,
            "circle_points": 8
        },
        "low": {
            "min_area": 300,
            "max_area": 30000,
            "morph_iterations": 1,
            "circle_points": 6
        }
    }
    
    # 自适应阈值
    ENABLE_AUTO_THRESHOLD = False  # 关闭自动阈值以保持稳定性
    THRESHOLD_ADAPTATION_RATE = 0.1

# ========================== 配置管理器 ==========================
class K230ConfigManager:
    """K230配置管理器"""
    
    def __init__(self):
        self.hardware = K230HardwareConfig()
        self.performance = K230PerformanceConfig()
        self.image_processing = K230ImageProcessingConfig()
        self.perspective = K230PerspectiveConfig()
        self.touch = K230TouchConfig()
        self.uart = K230UARTConfig()
        self.debug = K230DebugConfig()
        self.adaptive = K230AdaptiveConfig()
        
        self.current_quality_level = "medium"
        self.current_fps = 0
        
    def get_current_config(self):
        """获取当前配置"""
        return {
            "hardware": self.hardware,
            "performance": self.performance,
            "image_processing": self.image_processing,
            "perspective": self.perspective,
            "touch": self.touch,
            "uart": self.uart,
            "debug": self.debug,
            "adaptive": self.adaptive
        }
    
    def update_quality_level(self, level):
        """更新质量级别"""
        if level in self.adaptive.QUALITY_LEVELS:
            self.current_quality_level = level
            quality_config = self.adaptive.QUALITY_LEVELS[level]
            
            # 更新图像处理参数
            self.image_processing.MIN_CONTOUR_AREA = quality_config["min_area"]
            self.image_processing.MAX_CONTOUR_AREA = quality_config["max_area"]
            self.image_processing.MORPH_ITERATIONS = quality_config["morph_iterations"]
            self.perspective.CIRCLE_NUM_POINTS = quality_config["circle_points"]
            
            print(f"Quality level updated to: {level}")
    
    def adapt_performance(self, current_fps):
        """根据当前FPS自适应调整性能"""
        self.current_fps = current_fps
        
        if not self.adaptive.ENABLE_ADAPTIVE_PERFORMANCE:
            return
        
        if current_fps < self.adaptive.MIN_FPS:
            # 性能不足，降低质量
            if self.current_quality_level == "high":
                self.update_quality_level("medium")
            elif self.current_quality_level == "medium":
                self.update_quality_level("low")
        elif current_fps > self.adaptive.TARGET_FPS:
            # 性能充足，提高质量
            if self.current_quality_level == "low":
                self.update_quality_level("medium")
            elif self.current_quality_level == "medium":
                self.update_quality_level("high")

# ========================== 全局配置实例 ==========================
# 创建全局配置管理器实例
k230_config = K230ConfigManager()

print("K230 configuration module loaded successfully")
