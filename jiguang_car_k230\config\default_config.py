# default_config.py - K230默认配置文件
"""
K230默认配置文件
包含所有模块的默认参数设置
"""

# ========================== 硬件默认配置 ==========================
class DefaultHardwareConfig:
    """默认硬件配置"""
    
    # 摄像头默认配置
    CAMERA_WIDTH = 320
    CAMERA_HEIGHT = 240
    CAMERA_FPS = 30
    CAMERA_FORMAT = "RGB888"
    CAMERA_CSI_ID = 2  # 默认使用CSI2接口（GC2093）
    
    # 显示默认配置
    DISPLAY_WIDTH = 320
    DISPLAY_HEIGHT = 240
    DISPLAY_TYPE = "VIRT"  # 虚拟显示缓冲区
    
    # 串口默认配置
    UART_DEVICE = "/dev/ttyS0"
    UART_BAUDRATE = 115200
    UART_FRAME_HEADER = "$$"
    UART_FRAME_TAIL = "##"
    UART_ENABLE_FRAME = True
    
    # GPIO默认配置
    GPIO_BUTTON_PINS = {
        "center": 0,
        "circle": 1,
        "thresh_up": 2,
        "thresh_down": 3
    }

# ========================== 图像处理默认配置 ==========================
class DefaultImageConfig:
    """默认图像处理配置"""
    
    # 矩形检测参数
    MIN_CONTOUR_AREA = 300
    MAX_CONTOUR_AREA = 30000
    TARGET_SIDES = 4
    BINARY_THRESHOLD = 70
    
    # 宽高比过滤参数
    MIN_ASPECT_RATIO = 0.5
    MAX_ASPECT_RATIO = 2.0
    
    # 角度过滤参数
    MIN_ANGLE = 70
    MAX_ANGLE = 110
    
    # 对边长度一致性参数
    MIN_OPPOSITE_RATIO = 0.5
    MAX_OPPOSITE_RATIO = 1.5
    
    # 形态学操作参数
    MORPH_KERNEL_SIZE = 2
    MORPH_ITERATIONS = 1
    
    # 轮廓近似参数
    APPROX_EPSILON_FACTOR = 0.02

# ========================== 性能默认配置 ==========================
class DefaultPerformanceConfig:
    """默认性能配置"""
    
    # 处理优化
    ENABLE_MULTI_THREADING = False
    IMAGE_BUFFER_SIZE = 2
    PROCESS_EVERY_N_FRAMES = 1
    
    # 内存管理
    ENABLE_GARBAGE_COLLECTION = True
    GC_FREQUENCY = 30
    MAX_CONTOURS = 10
    
    # 算法优化
    USE_SIMPLIFIED_PERSPECTIVE = True
    SKIP_COMPLEX_VALIDATION = False
    ENABLE_EARLY_TERMINATION = True

# ========================== 触摸控制默认配置 ==========================
class DefaultTouchConfig:
    """默认触摸控制配置"""
    
    # 触摸防抖参数
    TOUCH_DEBOUNCE = 0.2
    
    # 虚拟按键配置
    BUTTONS = [
        [15, 200, 40, 18, "Center", "center"],
        [100, 200, 45, 18, "Circle", "circle"],
        [170, 200, 20, 18, "T-", "thresh_down"],
        [195, 200, 20, 18, "T+", "thresh_up"]
    ]
    
    # 触摸区域配置
    TOUCH_AREAS = [
        [30, 350, 80, 35],
        [200, 340, 90, 35],
        [320, 340, 45, 35],
        [370, 340, 45, 35]
    ]
    
    # 输入模式配置
    DEFAULT_INPUT_MODE = "touch"
    ENABLE_INPUT_FALLBACK = True

# ========================== 调试默认配置 ==========================
class DefaultDebugConfig:
    """默认调试配置"""
    
    # 日志配置
    ENABLE_DEBUG_LOG = True
    LOG_LEVEL = "INFO"
    
    # 性能监控
    ENABLE_FPS_MONITOR = True
    FPS_DISPLAY_INTERVAL = 30
    
    # 图像调试
    ENABLE_IMAGE_DEBUG = False
    SAVE_DEBUG_IMAGES = False
    
    # 内存监控
    ENABLE_MEMORY_MONITOR = True
    MEMORY_CHECK_INTERVAL = 60

# ========================== 透视变换默认配置 ==========================
class DefaultPerspectiveConfig:
    """默认透视变换配置"""
    
    # 校正后图像尺寸
    CORRECTED_WIDTH = 150
    CORRECTED_HEIGHT = 100
    
    # 圆形轨迹参数
    CIRCLE_RADIUS = 40
    CIRCLE_NUM_POINTS = 8
    
    # 透视变换优化
    USE_FAST_TRANSFORM = True
    CACHE_TRANSFORM_MATRIX = True

# ========================== 自适应默认配置 ==========================
class DefaultAdaptiveConfig:
    """默认自适应配置"""
    
    # 性能自适应
    ENABLE_ADAPTIVE_PERFORMANCE = True
    TARGET_FPS = 25
    MIN_FPS = 15
    
    # 质量自适应
    ENABLE_ADAPTIVE_QUALITY = True
    DEFAULT_QUALITY_LEVEL = "medium"
    
    # 质量级别定义
    QUALITY_LEVELS = {
        "high": {
            "min_area": 500,
            "max_area": 50000,
            "morph_iterations": 2,
            "circle_points": 12,
            "approx_epsilon": 0.015
        },
        "medium": {
            "min_area": 300,
            "max_area": 30000,
            "morph_iterations": 1,
            "circle_points": 8,
            "approx_epsilon": 0.02
        },
        "low": {
            "min_area": 200,
            "max_area": 20000,
            "morph_iterations": 1,
            "circle_points": 6,
            "approx_epsilon": 0.03
        }
    }
    
    # 自适应阈值
    ENABLE_AUTO_THRESHOLD = False
    THRESHOLD_ADAPTATION_RATE = 0.1

# ========================== 配置验证 ==========================
class ConfigValidator:
    """配置验证器"""
    
    @staticmethod
    def validate_hardware_config(config):
        """验证硬件配置"""
        errors = []
        
        if config.CAMERA_WIDTH <= 0 or config.CAMERA_HEIGHT <= 0:
            errors.append("摄像头尺寸必须大于0")
        
        if config.CAMERA_FPS <= 0 or config.CAMERA_FPS > 60:
            errors.append("摄像头帧率必须在1-60之间")
        
        if config.UART_BAUDRATE not in [9600, 19200, 38400, 57600, 115200]:
            errors.append("串口波特率不在支持范围内")
        
        return errors
    
    @staticmethod
    def validate_image_config(config):
        """验证图像处理配置"""
        errors = []
        
        if config.MIN_CONTOUR_AREA >= config.MAX_CONTOUR_AREA:
            errors.append("最小轮廓面积必须小于最大轮廓面积")
        
        if config.MIN_ASPECT_RATIO >= config.MAX_ASPECT_RATIO:
            errors.append("最小宽高比必须小于最大宽高比")
        
        if config.MIN_ANGLE >= config.MAX_ANGLE:
            errors.append("最小角度必须小于最大角度")
        
        if config.BINARY_THRESHOLD < 0 or config.BINARY_THRESHOLD > 255:
            errors.append("二值化阈值必须在0-255之间")
        
        return errors
    
    @staticmethod
    def validate_all_configs():
        """验证所有配置"""
        all_errors = []
        
        # 验证各个配置模块
        hw_errors = ConfigValidator.validate_hardware_config(DefaultHardwareConfig())
        img_errors = ConfigValidator.validate_image_config(DefaultImageConfig())
        
        all_errors.extend([f"硬件配置: {e}" for e in hw_errors])
        all_errors.extend([f"图像配置: {e}" for e in img_errors])
        
        return all_errors

# ========================== 配置导出 ==========================
def get_default_config():
    """获取默认配置字典"""
    return {
        "hardware": DefaultHardwareConfig(),
        "image": DefaultImageConfig(),
        "performance": DefaultPerformanceConfig(),
        "touch": DefaultTouchConfig(),
        "debug": DefaultDebugConfig(),
        "perspective": DefaultPerspectiveConfig(),
        "adaptive": DefaultAdaptiveConfig()
    }

def validate_config():
    """验证配置有效性"""
    errors = ConfigValidator.validate_all_configs()
    if errors:
        print("配置验证失败:")
        for error in errors:
            print(f"  - {error}")
        return False
    else:
        print("配置验证通过")
        return True

# ========================== 配置使用示例 ==========================
if __name__ == "__main__":
    print("K230默认配置文件")
    print("=" * 40)
    
    # 验证配置
    if validate_config():
        config = get_default_config()
        
        print("\n硬件配置:")
        hw = config["hardware"]
        print(f"  摄像头: {hw.CAMERA_WIDTH}x{hw.CAMERA_HEIGHT}@{hw.CAMERA_FPS}fps")
        print(f"  串口: {hw.UART_DEVICE}@{hw.UART_BAUDRATE}")
        
        print("\n图像处理配置:")
        img = config["image"]
        print(f"  轮廓面积: {img.MIN_CONTOUR_AREA}-{img.MAX_CONTOUR_AREA}")
        print(f"  二值化阈值: {img.BINARY_THRESHOLD}")
        
        print("\n性能配置:")
        perf = config["performance"]
        print(f"  垃圾回收: {'启用' if perf.ENABLE_GARBAGE_COLLECTION else '禁用'}")
        print(f"  最大轮廓数: {perf.MAX_CONTOURS}")
        
        print("\n配置加载完成")
    else:
        print("配置验证失败，请检查配置参数")
