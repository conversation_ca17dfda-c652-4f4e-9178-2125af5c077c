# MaixCAM到庐山派K230移植指南

## 项目概述

本项目将原有的MaixCAM智能车视觉识别代码成功移植到庐山派K230平台，保持了与STM32的完全通信兼容性。

## 硬件要求

- **开发板**: 庐山派K230
- **摄像头**: GC2093 (官方标配)
- **显示屏**: 官方LCD显示屏 (支持触摸)
- **通信**: STM32设备 (通过UART)

## 文件结构

```
├── main_k230_final.py      # K230最终优化版主程序
├── k230_compat.py          # MaixCAM到K230的API适配层
├── k230_image_processing.py # K230图像处理模块 (替代OpenCV)
├── k230_display_touch.py   # K230显示和触摸适配模块
├── k230_uart_adapter.py    # K230串口通信适配模块
├── k230_config.py          # K230优化配置文件
├── micu_uart_lib/          # 原有串口通信库 (保持兼容)
└── README_K230.md          # 本说明文档
```

## 核心适配内容

### 1. API适配层 (k230_compat.py)
- 将 `maix.camera` 适配为 `media.sensor`
- 将 `maix.display` 适配为 `media.display`
- 提供兼容的图像和时间接口

### 2. 图像处理适配 (k230_image_processing.py)
- 替代OpenCV的核心功能
- 实现二值化、轮廓检测、形态学操作
- 提供几何计算和透视变换工具
- 兼容的绘图函数

### 3. 显示和触摸适配 (k230_display_touch.py)
- 适配K230的LCD显示系统
- 支持触摸屏和GPIO按键两种输入模式
- 提供输入管理器统一接口

### 4. 串口通信适配 (k230_uart_adapter.py)
- 保持与原有STM32通信协议的完全兼容
- 支持帧格式化数据传输
- 线程安全的数据收发

### 5. 性能优化配置 (k230_config.py)
- 针对K230性能特点的参数优化
- 自适应质量调整机制
- 内存和性能监控

## 使用方法

### 1. 环境准备
确保庐山派K230已安装CanMV固件和Python环境。

### 2. 文件部署
将所有Python文件复制到K230的工作目录。

### 3. 运行程序
```bash
python main_k230_final.py
```

### 4. 操作说明
- **触摸控制**: 使用屏幕下方的虚拟按键
  - `Center`: 切换到中心点模式
  - `Circle`: 切换到圆形轨迹模式
  - `T-/T+`: 调整二值化阈值
- **GPIO控制**: 如果触摸屏不可用，可切换到GPIO按键模式

## 功能特性

### 保持的原有功能
- ✅ 矩形检测和识别
- ✅ 中心点计算和输出
- ✅ 圆形轨迹生成
- ✅ 触摸屏虚拟按键控制
- ✅ 串口通信协议 (与STM32兼容)
- ✅ 实时图像显示
- ✅ 参数动态调整

### K230平台优化
- 🚀 针对K230性能的算法优化
- 🚀 自适应质量调整 (高/中/低三档)
- 🚀 内存管理和垃圾回收优化
- 🚀 多输入模式支持 (触摸/GPIO)
- 🚀 性能监控和调试功能

## 配置说明

### 图像处理参数
```python
# 在k230_config.py中调整
MIN_CONTOUR_AREA = 300      # 最小轮廓面积
MAX_CONTOUR_AREA = 30000    # 最大轮廓面积
BINARY_THRESHOLD = 70       # 二值化阈值
```

### 性能优化参数
```python
# 质量级别: "high", "medium", "low"
TARGET_FPS = 25             # 目标帧率
ENABLE_ADAPTIVE_PERFORMANCE = True  # 自适应性能调整
```

### 通信协议参数
```python
UART_DEVICE = "/dev/ttyS0"  # 串口设备
UART_BAUDRATE = 115200      # 波特率
FRAME_HEADER = "$$"         # 帧头
FRAME_TAIL = "##"           # 帧尾
```

## 串口通信协议

### 数据格式
与原有STM32通信协议完全兼容：

**中心点模式**:
```
$$R,x,y##
```

**圆形轨迹模式**:
```
$$C,count,x1,y1,x2,y2,...##
```

## 性能特点

### 优化效果
- **帧率**: 目标25FPS，最低15FPS
- **延迟**: 低于50ms的处理延迟
- **内存**: 优化的内存使用和垃圾回收
- **稳定性**: 长时间运行稳定

### 自适应机制
程序会根据实际运行性能自动调整：
- 高性能时提升图像质量
- 性能不足时降低计算复杂度
- 保证最低可用帧率

## 故障排除

### 常见问题

1. **摄像头初始化失败**
   - 检查GC2093摄像头连接
   - 确认CSI接口配置正确

2. **触摸屏无响应**
   - 尝试切换到GPIO按键模式
   - 检查触摸屏驱动安装

3. **串口通信失败**
   - 确认串口设备路径 `/dev/ttyS0`
   - 检查波特率设置 `115200`
   - 验证STM32端协议兼容性

4. **性能不佳**
   - 调整质量级别到 "low"
   - 减少处理的轮廓数量
   - 启用性能优化选项

### 调试模式
```python
# 在k230_config.py中启用调试
ENABLE_DEBUG_LOG = True
ENABLE_FPS_MONITOR = True
ENABLE_MEMORY_MONITOR = True
```

## 技术支持

### 开发环境
- **平台**: 庐山派K230
- **固件**: CanMV K230
- **语言**: MicroPython
- **依赖**: media.sensor, media.display, media.media

### 兼容性说明
- ✅ 完全兼容原有STM32通信协议
- ✅ 保持原有功能逻辑不变
- ✅ 支持原有参数配置方式
- ❌ 不支持OpenCV (已用原生API替代)
- ❌ 不支持MaixCAM特有功能

## 更新日志

### v1.0.0 (当前版本)
- 完成MaixCAM到K230的完整移植
- 实现所有核心功能的适配
- 添加性能优化和自适应机制
- 保持STM32通信协议兼容性

---

**注意**: 本移植版本专为庐山派K230平台优化，确保在部署前已正确配置K230开发环境。
