# touch_test.py - K230触摸屏测试
"""
K230触摸屏和输入系统测试程序
用于验证触摸屏和GPIO按键功能是否正常
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from k230_adapters import k230_input, k230_display, K230Image, Camera
import time

def test_touch_init():
    """测试触摸屏初始化"""
    print("=== K230触摸屏初始化测试 ===")
    
    try:
        print("1. 测试触摸模式初始化...")
        result = k230_input.init("touch")
        if result:
            print("   ✅ 触摸模式初始化成功")
        else:
            print("   ❌ 触摸模式初始化失败")
            return False
        
        print("2. 测试GPIO模式初始化...")
        result = k230_input.init("gpio")
        if result:
            print("   ✅ GPIO模式初始化成功")
        else:
            print("   ⚠️  GPIO模式初始化失败（可能是正常的）")
        
        # 切换回触摸模式
        k230_input.init("touch")
        print("   ✅ 切换回触摸模式")
        
        return True
        
    except Exception as e:
        print(f"❌ 触摸屏初始化测试失败: {e}")
        return False

def test_touch_reading():
    """测试触摸数据读取"""
    print("\n=== K230触摸数据读取测试 ===")
    
    try:
        print("1. 测试触摸数据读取...")
        print("   请在屏幕上进行触摸操作（10秒内）...")
        
        start_time = time.time()
        touch_count = 0
        last_touch_time = 0
        
        while time.time() - start_time < 10:  # 测试10秒
            touch_data = k230_input.read_input()
            if len(touch_data) >= 3:
                x, y, pressed = touch_data[0], touch_data[1], touch_data[2]
                
                current_time = time.time()
                if pressed and (current_time - last_touch_time > 0.5):  # 防抖
                    touch_count += 1
                    print(f"   触摸 #{touch_count}: 位置({x}, {y})")
                    last_touch_time = current_time
            
            time.sleep(0.05)  # 20Hz读取频率
        
        print(f"   检测到 {touch_count} 次有效触摸")
        
        if touch_count > 0:
            print("   ✅ 触摸数据读取成功")
            return True
        else:
            print("   ⚠️  未检测到触摸（可能是正常的）")
            return True  # 没有触摸也算正常
        
    except Exception as e:
        print(f"❌ 触摸数据读取测试失败: {e}")
        return False

def test_virtual_buttons():
    """测试虚拟按键功能"""
    print("\n=== K230虚拟按键测试 ===")
    
    try:
        # 导入虚拟按键类
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from main_k230_final import K230VirtualButtons
        
        print("1. 初始化虚拟按键...")
        buttons = K230VirtualButtons()
        print("   ✅ 虚拟按键初始化成功")
        
        print("2. 测试按键区域检测...")
        
        # 测试各个按键区域
        test_positions = [
            (50, 380, "center"),    # Center按键区域
            (250, 370, "circle"),   # Circle按键区域
            (380, 370, "thresh_up"), # T+按键区域
            (340, 370, "thresh_down") # T-按键区域
        ]
        
        for x, y, expected in test_positions:
            action = buttons.check_touch(x, y)
            if action == expected:
                print(f"   ✅ 位置({x}, {y}) -> {action} (正确)")
            else:
                print(f"   ❌ 位置({x}, {y}) -> {action} (期望: {expected})")
        
        print("3. 测试按键防抖...")
        # 快速连续触摸同一位置
        for i in range(5):
            action = buttons.check_touch(50, 380)
            if i == 0:
                expected_result = "center"
            else:
                expected_result = None  # 防抖应该阻止后续触摸
            
            if action == expected_result:
                print(f"   ✅ 防抖测试 #{i+1}: {action} (正确)")
            else:
                print(f"   ❌ 防抖测试 #{i+1}: {action} (期望: {expected_result})")
            
            time.sleep(0.1)
        
        print("✅ 虚拟按键测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 虚拟按键测试失败: {e}")
        return False

def test_input_modes():
    """测试输入模式切换"""
    print("\n=== K230输入模式测试 ===")
    
    try:
        print("1. 测试模式切换...")
        
        # 测试触摸模式
        result = k230_input.switch_mode("touch")
        if result:
            print("   ✅ 切换到触摸模式成功")
        else:
            print("   ❌ 切换到触摸模式失败")
            return False
        
        # 测试GPIO模式
        result = k230_input.switch_mode("gpio")
        if result:
            print("   ✅ 切换到GPIO模式成功")
        else:
            print("   ⚠️  切换到GPIO模式失败（可能是正常的）")
        
        # 测试无效模式
        result = k230_input.switch_mode("invalid")
        if not result:
            print("   ✅ 无效模式正确拒绝")
        else:
            print("   ❌ 无效模式错误接受")
            return False
        
        # 恢复触摸模式
        k230_input.switch_mode("touch")
        
        print("✅ 输入模式测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 输入模式测试失败: {e}")
        return False

def test_interactive_touch():
    """交互式触摸测试"""
    print("\n=== K230交互式触摸测试 ===")
    
    try:
        # 初始化显示和摄像头
        print("1. 初始化显示系统...")
        disp = k230_display
        if not disp.init(320, 240):
            print("   ❌ 显示初始化失败")
            return False
        
        cam = Camera(320, 240)
        print("   ✅ 显示和摄像头初始化成功")
        
        print("2. 开始交互式测试（按任意键退出）...")
        print("   请触摸屏幕，触摸位置将显示在图像上")
        
        # 导入绘图工具
        from k230_adapters import drawing, FONT_HERSHEY_SIMPLEX
        
        test_duration = 15  # 测试15秒
        start_time = time.time()
        touch_points = []
        
        while time.time() - start_time < test_duration:
            # 读取图像
            img = cam.read()
            if not img:
                continue
            
            output = img.img.copy()
            
            # 读取触摸
            touch_data = k230_input.read_input()
            if len(touch_data) >= 3:
                x, y, pressed = touch_data[0], touch_data[1], touch_data[2]
                
                if pressed:
                    # 记录触摸点
                    current_time = time.time()
                    touch_points.append((x, y, current_time))
                    print(f"   触摸: ({x}, {y})")
            
            # 绘制触摸点（显示最近5秒的触摸）
            current_time = time.time()
            active_points = [(x, y) for x, y, t in touch_points 
                           if current_time - t < 5.0]
            
            for i, (x, y) in enumerate(active_points):
                # 绘制触摸点
                drawing.draw_circle(output, (x, y), 5, (0, 255, 0), -1)
                # 绘制序号
                drawing.put_text(output, str(i+1), (x+8, y-8), 
                               FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 0), 1)
            
            # 绘制说明文字
            drawing.put_text(output, f"Touch Test: {len(active_points)} points", 
                           (10, 20), FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
            drawing.put_text(output, f"Time: {test_duration - (current_time - start_time):.1f}s", 
                           (10, 40), FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
            
            # 显示图像
            disp.show(K230Image(output))
            time.sleep(0.05)
        
        print(f"   交互式测试完成，共记录 {len(touch_points)} 个触摸点")
        print("✅ 交互式触摸测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 交互式触摸测试失败: {e}")
        return False

def test_touch_calibration():
    """测试触摸屏校准"""
    print("\n=== K230触摸屏校准测试 ===")
    
    try:
        print("1. 测试触摸屏校准功能...")
        
        # 尝试校准触摸屏
        result = k230_input.touchscreen.calibrate()
        if result:
            print("   ✅ 触摸屏校准成功")
        else:
            print("   ⚠️  触摸屏校准失败（可能不支持）")
        
        print("2. 测试校准后的精度...")
        # 这里可以添加校准精度测试
        
        print("✅ 触摸屏校准测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 触摸屏校准测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("K230触摸屏和输入系统测试开始...\n")
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("触摸屏初始化", test_touch_init()))
    test_results.append(("触摸数据读取", test_touch_reading()))
    test_results.append(("虚拟按键功能", test_virtual_buttons()))
    test_results.append(("输入模式切换", test_input_modes()))
    test_results.append(("交互式触摸", test_interactive_touch()))
    test_results.append(("触摸屏校准", test_touch_calibration()))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("触摸屏测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("="*50)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有触摸测试通过！输入系统工作正常")
    elif passed >= total * 0.7:  # 70%通过率也算可接受
        print("⚠️  大部分测试通过，输入系统基本可用")
    else:
        print("❌ 多项测试失败，请检查触摸屏硬件和驱动")
    
    return passed >= total * 0.7

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        exit(1)
    except Exception as e:
        print(f"\n测试程序异常: {e}")
        exit(1)
